/**
 * Critical Feature Test
 * This test will FAIL if any of the claimed fixes are not properly implemented
 */

class CriticalFeatureTest {
  constructor() {
    this.failures = [];
    this.testsPassed = 0;
    this.testsTotal = 0;
  }

  async runCriticalTests() {
    console.log('🔥 Running CRITICAL Feature Tests - These MUST pass!');
    
    // Test 1: Side Panel Implementation
    await this.testSidePanelImplementation();
    
    // Test 2: Loading State Management
    await this.testLoadingStateManagement();
    
    // Test 3: Message Persistence
    await this.testMessagePersistenceImplementation();
    
    // Test 4: Timeout Handling
    await this.testTimeoutHandling();
    
    // Test 5: GroqCloud Integration
    await this.testGroqCloudImplementation();
    
    // Test 6: Error Handling
    await this.testErrorHandlingImplementation();
    
    // Test 7: UI Elements
    await this.testUIElements();
    
    return this.generateCriticalReport();
  }

  async testSidePanelImplementation() {
    console.log('📋 Testing Side Panel Implementation...');
    
    this.testsTotal += 5;
    
    // Test 1.1: <PERSON><PERSON><PERSON> has sidePanel permission
    const manifest = chrome.runtime.getManifest();
    if (manifest.permissions.includes('sidePanel')) {
      this.testsPassed++;
      console.log('✅ Side panel permission exists');
    } else {
      this.failures.push('CRITICAL: sidePanel permission missing from manifest.json');
    }
    
    // Test 1.2: Manifest has side_panel configuration
    if (manifest.side_panel && manifest.side_panel.default_path) {
      this.testsPassed++;
      console.log('✅ Side panel configuration exists');
    } else {
      this.failures.push('CRITICAL: side_panel configuration missing from manifest.json');
    }
    
    // Test 1.3: Side panel toggle button exists in DOM
    const sidePanelBtn = document.querySelector('[title="Open in Side Panel"]');
    if (sidePanelBtn) {
      this.testsPassed++;
      console.log('✅ Side panel toggle button found in DOM');
    } else {
      this.failures.push('CRITICAL: Side panel toggle button not found in popup');
    }
    
    // Test 1.4: Side panel CSS exists
    const hasCSS = this.checkCSSRule('side-panel-mode');
    if (hasCSS) {
      this.testsPassed++;
      console.log('✅ Side panel CSS styles exist');
    } else {
      this.failures.push('CRITICAL: Side panel CSS styles missing');
    }
    
    // Test 1.5: Side panel JavaScript methods exist
    if (typeof window.popupManager !== 'undefined' && 
        window.popupManager.setupSidePanel && 
        window.popupManager.addSidePanelToggle) {
      this.testsPassed++;
      console.log('✅ Side panel JavaScript methods exist');
    } else {
      this.failures.push('CRITICAL: Side panel JavaScript methods missing');
    }
  }

  async testLoadingStateManagement() {
    console.log('⏳ Testing Loading State Management...');
    
    this.testsTotal += 4;
    
    // Test 2.1: Loading state flag exists
    if (typeof window.popupManager !== 'undefined' && 
        'isLoading' in window.popupManager) {
      this.testsPassed++;
      console.log('✅ Loading state flag exists');
    } else {
      this.failures.push('CRITICAL: Loading state flag (isLoading) not implemented');
    }
    
    // Test 2.2: Send button disables during loading
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
      this.testsPassed++;
      console.log('✅ Send button exists for loading state test');
    } else {
      this.failures.push('CRITICAL: Send button not found');
    }
    
    // Test 2.3: Loading overlay exists
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
      this.testsPassed++;
      console.log('✅ Loading overlay exists');
    } else {
      this.failures.push('CRITICAL: Loading overlay not found');
    }
    
    // Test 2.4: Timeout handling in sendMessage
    const popupScript = await this.getScriptContent('popup/popup.js');
    if (popupScript.includes('Promise.race') && popupScript.includes('timeout')) {
      this.testsPassed++;
      console.log('✅ Timeout handling implemented');
    } else {
      this.failures.push('CRITICAL: Timeout handling not implemented in sendMessage');
    }
  }

  async testMessagePersistenceImplementation() {
    console.log('💾 Testing Message Persistence Implementation...');
    
    this.testsTotal += 5;
    
    // Test 3.1: currentSessionMessages array exists
    if (typeof window.popupManager !== 'undefined' && 
        Array.isArray(window.popupManager.currentSessionMessages)) {
      this.testsPassed++;
      console.log('✅ currentSessionMessages array exists');
    } else {
      this.failures.push('CRITICAL: currentSessionMessages array not implemented');
    }
    
    // Test 3.2: saveCurrentSession method exists
    if (typeof window.popupManager !== 'undefined' && 
        typeof window.popupManager.saveCurrentSession === 'function') {
      this.testsPassed++;
      console.log('✅ saveCurrentSession method exists');
    } else {
      this.failures.push('CRITICAL: saveCurrentSession method not implemented');
    }
    
    // Test 3.3: restoreCurrentSession method exists
    if (typeof window.popupManager !== 'undefined' && 
        typeof window.popupManager.restoreCurrentSession === 'function') {
      this.testsPassed++;
      console.log('✅ restoreCurrentSession method exists');
    } else {
      this.failures.push('CRITICAL: restoreCurrentSession method not implemented');
    }
    
    // Test 3.4: Clear chat button exists
    const clearChatBtn = document.getElementById('clearChatBtn');
    if (clearChatBtn) {
      this.testsPassed++;
      console.log('✅ Clear chat button exists');
    } else {
      this.failures.push('CRITICAL: Clear chat button not found');
    }
    
    // Test 3.5: Storage functionality works
    try {
      await chrome.storage.local.set({ testKey: 'testValue' });
      const result = await chrome.storage.local.get(['testKey']);
      if (result.testKey === 'testValue') {
        this.testsPassed++;
        console.log('✅ Storage functionality works');
        await chrome.storage.local.remove(['testKey']);
      } else {
        this.failures.push('CRITICAL: Storage functionality not working');
      }
    } catch (error) {
      this.failures.push(`CRITICAL: Storage error - ${error.message}`);
    }
  }

  async testTimeoutHandling() {
    console.log('⏰ Testing Timeout Handling...');
    
    this.testsTotal += 3;
    
    // Test 4.1: Background script timeout
    const backgroundScript = await this.getScriptContent('background/background.js');
    if (backgroundScript.includes('timeoutPromise') || backgroundScript.includes('setTimeout')) {
      this.testsPassed++;
      console.log('✅ Background script timeout handling exists');
    } else {
      this.failures.push('CRITICAL: Background script timeout handling missing');
    }
    
    // Test 4.2: Content script timeout
    const contentScript = await this.getScriptContent('content/content.js');
    if (contentScript.includes('setTimeout') && contentScript.includes('30000')) {
      this.testsPassed++;
      console.log('✅ Content script timeout handling exists');
    } else {
      this.failures.push('CRITICAL: Content script timeout handling missing');
    }
    
    // Test 4.3: Popup script timeout
    const popupScript = await this.getScriptContent('popup/popup.js');
    if (popupScript.includes('30000') && popupScript.includes('timeout')) {
      this.testsPassed++;
      console.log('✅ Popup script timeout handling exists');
    } else {
      this.failures.push('CRITICAL: Popup script timeout handling missing');
    }
  }

  async testGroqCloudImplementation() {
    console.log('⚡ Testing GroqCloud Implementation...');
    
    this.testsTotal += 4;
    
    // Test 5.1: GroqCloud provider exists
    const llmScript = await this.getScriptContent('lib/llm-api.js');
    if (llmScript.includes('groq') && llmScript.includes('api.groq.com')) {
      this.testsPassed++;
      console.log('✅ GroqCloud provider implemented');
    } else {
      this.failures.push('CRITICAL: GroqCloud provider not implemented');
    }
    
    // Test 5.2: GroqCloud models defined
    if (llmScript.includes('llama-3.3-70b-versatile')) {
      this.testsPassed++;
      console.log('✅ GroqCloud models defined');
    } else {
      this.failures.push('CRITICAL: GroqCloud models not defined');
    }
    
    // Test 5.3: callGroq method exists
    if (llmScript.includes('callGroq')) {
      this.testsPassed++;
      console.log('✅ callGroq method implemented');
    } else {
      this.failures.push('CRITICAL: callGroq method not implemented');
    }
    
    // Test 5.4: GroqCloud in settings
    const settingsHTML = await this.getFileContent('settings/settings.html');
    if (settingsHTML.includes('GroqCloud') || settingsHTML.includes('groq')) {
      this.testsPassed++;
      console.log('✅ GroqCloud option in settings');
    } else {
      this.failures.push('CRITICAL: GroqCloud option missing from settings');
    }
  }

  async testErrorHandlingImplementation() {
    console.log('🚨 Testing Error Handling Implementation...');
    
    this.testsTotal += 3;
    
    // Test 6.1: Notifications permission
    const manifest = chrome.runtime.getManifest();
    if (manifest.permissions.includes('notifications')) {
      this.testsPassed++;
      console.log('✅ Notifications permission exists');
    } else {
      this.failures.push('CRITICAL: Notifications permission missing');
    }
    
    // Test 6.2: Fallback methods in background
    const backgroundScript = await this.getScriptContent('background/background.js');
    if (backgroundScript.includes('showFallbackResponse') || backgroundScript.includes('showFallbackError')) {
      this.testsPassed++;
      console.log('✅ Fallback error methods exist');
    } else {
      this.failures.push('CRITICAL: Fallback error methods missing');
    }
    
    // Test 6.3: Error message display
    const popupScript = await this.getScriptContent('popup/popup.js');
    if (popupScript.includes('addMessageToChat') && popupScript.includes('error')) {
      this.testsPassed++;
      console.log('✅ Error message display implemented');
    } else {
      this.failures.push('CRITICAL: Error message display missing');
    }
  }

  async testUIElements() {
    console.log('🎨 Testing UI Elements...');
    
    this.testsTotal += 6;
    
    // Test 7.1: Chat messages container
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
      this.testsPassed++;
      console.log('✅ Chat messages container exists');
    } else {
      this.failures.push('CRITICAL: Chat messages container missing');
    }
    
    // Test 7.2: Question input
    const questionInput = document.getElementById('questionInput');
    if (questionInput) {
      this.testsPassed++;
      console.log('✅ Question input exists');
    } else {
      this.failures.push('CRITICAL: Question input missing');
    }
    
    // Test 7.3: Send button
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
      this.testsPassed++;
      console.log('✅ Send button exists');
    } else {
      this.failures.push('CRITICAL: Send button missing');
    }
    
    // Test 7.4: Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      this.testsPassed++;
      console.log('✅ Settings button exists');
    } else {
      this.failures.push('CRITICAL: Settings button missing');
    }
    
    // Test 7.5: History button
    const historyBtn = document.getElementById('historyBtn');
    if (historyBtn) {
      this.testsPassed++;
      console.log('✅ History button exists');
    } else {
      this.failures.push('CRITICAL: History button missing');
    }
    
    // Test 7.6: Status indicator
    const statusIndicator = document.getElementById('statusIndicator');
    if (statusIndicator) {
      this.testsPassed++;
      console.log('✅ Status indicator exists');
    } else {
      this.failures.push('CRITICAL: Status indicator missing');
    }
  }

  // Helper methods
  async getFileContent(path) {
    try {
      const response = await fetch(chrome.runtime.getURL(path));
      return await response.text();
    } catch (error) {
      throw new Error(`Failed to load ${path}: ${error.message}`);
    }
  }

  async getScriptContent(path) {
    return await this.getFileContent(path);
  }

  checkCSSRule(selector) {
    const stylesheets = Array.from(document.styleSheets);
    for (const sheet of stylesheets) {
      try {
        const rules = Array.from(sheet.cssRules || []);
        if (rules.some(rule => rule.selectorText && rule.selectorText.includes(selector))) {
          return true;
        }
      } catch (e) {
        // Cross-origin stylesheet, skip
      }
    }
    return false;
  }

  generateCriticalReport() {
    const successRate = (this.testsPassed / this.testsTotal) * 100;
    const overallStatus = this.failures.length === 0 ? 'ALL CRITICAL TESTS PASSED' : 'CRITICAL TESTS FAILED';
    
    console.log('\n🔥 CRITICAL TEST REPORT');
    console.log('========================');
    console.log(`Tests Passed: ${this.testsPassed}/${this.testsTotal}`);
    console.log(`Success Rate: ${successRate.toFixed(1)}%`);
    console.log(`Status: ${overallStatus}`);
    
    if (this.failures.length > 0) {
      console.log('\n❌ CRITICAL FAILURES:');
      this.failures.forEach((failure, index) => {
        console.log(`${index + 1}. ${failure}`);
      });
      console.log('\n🚨 EXTENSION IS NOT READY - THESE ISSUES MUST BE FIXED!');
    } else {
      console.log('\n✅ ALL CRITICAL FEATURES IMPLEMENTED CORRECTLY!');
    }
    
    return {
      passed: this.testsPassed,
      total: this.testsTotal,
      successRate: successRate,
      failures: this.failures,
      status: overallStatus,
      ready: this.failures.length === 0
    };
  }
}

// Auto-run if in popup context
if (typeof chrome !== 'undefined' && chrome.runtime) {
  // Wait for popup to load
  setTimeout(async () => {
    const criticalTest = new CriticalFeatureTest();
    const result = await criticalTest.runCriticalTests();
    
    if (!result.ready) {
      console.error('🚨 EXTENSION NOT READY FOR RELEASE!');
      console.error('Fix the critical failures listed above.');
    } else {
      console.log('🎉 EXTENSION READY FOR RELEASE!');
    }
  }, 1000);
}

// Export for manual testing
if (typeof window !== 'undefined') {
  window.CriticalFeatureTest = CriticalFeatureTest;
}
