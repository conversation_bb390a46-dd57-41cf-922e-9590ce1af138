# How to Run Extension Tests

## 🧪 Test Suite Overview

This comprehensive test suite validates all claimed features and fixes in the Universal AI Mentor extension:

1. **Automated Tests** - Code analysis and feature detection
2. **Manual Tests** - User interaction and functionality verification  
3. **Feature Verification** - Validates claimed features actually exist
4. **Performance Tests** - Measures response times and stability

## 📋 Running the Tests

### Method 1: Automated Test Runner (Recommended)

1. **Load the extension** in Chrome/Edge developer mode
2. **Open the test runner**: Navigate to `chrome-extension://[extension-id]/tests/automated-test-runner.html`
3. **Click "Run All Tests"** to execute comprehensive test suite
4. **Review results** in the interface

### Method 2: Manual Test Checklist

1. **Open** `tests/manual-test-checklist.md`
2. **Follow each test** step-by-step
3. **Check off** completed tests
4. **Note any failures** for debugging

### Method 3: Console Testing

1. **Open extension popup**
2. **Open browser console** (F12)
3. **Run individual tests**:

```javascript
// Load and run automated tests
const tester = new ExtensionTester();
tester.runAllTests();

// Run feature verification
const verifier = new FeatureVerifier();
verifier.verifyAllFeatures();
```

## ✅ Test Categories

### 1. Side Panel Stability Tests
- [ ] Side panel permission exists
- [ ] Side panel configuration in manifest
- [ ] Toggle button in popup header
- [ ] CSS styles for side panel mode
- [ ] JavaScript methods implemented

### 2. Loading State Fix Tests
- [ ] Loading state flag (`this.isLoading`)
- [ ] Timeout handling with `Promise.race`
- [ ] 30-second timeout implementation
- [ ] Background script timeout handling
- [ ] Content script auto-hide timeout

### 3. Message Persistence Tests
- [ ] `saveCurrentSession` method exists
- [ ] `restoreCurrentSession` method exists
- [ ] `currentSessionMessages` array
- [ ] Storage implementation with `chrome.storage.local`
- [ ] Clear chat functionality

### 4. Chat History Interface Tests
- [ ] History screen in HTML
- [ ] History button and content area
- [ ] `loadHistoryContent` method
- [ ] `createHistoryItem` method
- [ ] Screen navigation methods

### 5. Text Selection Feature Tests
- [ ] Context menu permission
- [ ] Context menu creation code
- [ ] Click handler implementation
- [ ] Fallback notification methods
- [ ] Content script message handling

### 6. GroqCloud Integration Tests
- [ ] GroqCloud provider in LLM client
- [ ] API endpoint configuration
- [ ] Model definitions (llama-3.3-70b-versatile, etc.)
- [ ] `callGroq` method implementation
- [ ] Settings page integration

### 7. Error Handling Tests
- [ ] Try-catch blocks in scripts
- [ ] Error message display
- [ ] Timeout error handling
- [ ] Graceful degradation

## 🎯 Critical Test Points

### Must Pass Tests
These tests MUST pass for the extension to be considered functional:

1. **No Infinite Loading**: Loading spinners must resolve within 30 seconds
2. **Message Persistence**: Conversations must survive popup close/reopen
3. **Side Panel Stability**: Popup must remain stable when clicking elsewhere
4. **Text Selection Works**: Context menu must show AI responses
5. **GroqCloud Speed**: Responses must be under 0.5 seconds with GroqCloud
6. **Error Handling**: Clear error messages, no crashes

### Performance Benchmarks
- **GroqCloud Response Time**: < 0.5 seconds
- **Other Providers**: 1-3 seconds acceptable
- **Timeout Handling**: 30 seconds maximum
- **Session Restore**: < 1 second
- **UI Responsiveness**: Immediate feedback

## 🔧 Test Execution Commands

### Quick Test (5 minutes)
```bash
# Test core functionality only
1. Load extension
2. Configure GroqCloud API
3. Send test message (check speed)
4. Close/reopen popup (check persistence)
5. Test text selection on webpage
```

### Full Test Suite (15 minutes)
```bash
# Complete verification
1. Run automated test runner
2. Execute manual checklist
3. Test all providers
4. Test error scenarios
5. Verify all UI elements
```

### Debug Mode Testing
```bash
# For troubleshooting
1. Open browser console
2. Check for JavaScript errors
3. Verify storage contents
4. Test API connections
5. Monitor network requests
```

## 📊 Expected Results

### Passing Test Suite
- **All automated tests pass** (100% success rate)
- **No critical issues** in feature verification
- **All manual tests check out**
- **Performance benchmarks met**

### Test Report Format
```
📊 TEST REPORT
================
Total Tests: 45
Passed: 43
Failed: 2
Success Rate: 95.6%

✅ VERIFIED FEATURES:
- Side Panel Stability
- Loading State Fix
- Message Persistence
- GroqCloud Integration

❌ FAILED FEATURES:
- Text Selection (timeout issues)
- Error Handling (missing fallbacks)
```

## 🚨 Failure Investigation

### If Tests Fail

1. **Check Console Errors**:
   ```javascript
   // In popup console
   console.log('Popup errors:', chrome.runtime.lastError);
   
   // In background console  
   console.log('Background errors:', chrome.runtime.lastError);
   ```

2. **Verify File Existence**:
   ```javascript
   // Check if files exist
   fetch(chrome.runtime.getURL('popup/popup.js'))
     .then(r => console.log('popup.js exists:', r.ok));
   ```

3. **Test Storage**:
   ```javascript
   // Test storage functionality
   chrome.storage.local.set({test: 'value'})
     .then(() => chrome.storage.local.get(['test']))
     .then(console.log);
   ```

4. **Check Permissions**:
   ```javascript
   // Verify permissions
   console.log('Manifest:', chrome.runtime.getManifest());
   ```

## 🎯 Test Completion Criteria

### Extension is Ready When:
- [ ] **100% automated test pass rate**
- [ ] **All manual tests completed successfully**
- [ ] **No critical issues in feature verification**
- [ ] **GroqCloud responses under 0.5 seconds**
- [ ] **Message persistence works reliably**
- [ ] **Side panel/popup stability confirmed**
- [ ] **Text selection feature functional**
- [ ] **Error handling graceful and informative**

### Extension Needs Work When:
- [ ] Any automated test fails
- [ ] Critical issues found in verification
- [ ] Infinite loading states observed
- [ ] Messages don't persist between sessions
- [ ] Text selection doesn't work
- [ ] API responses take too long
- [ ] Errors crash the extension

## 📝 Test Documentation

After running tests, document:
1. **Test execution date/time**
2. **Browser version and OS**
3. **Extension version tested**
4. **API provider used for testing**
5. **Pass/fail status for each category**
6. **Any issues encountered**
7. **Performance measurements**
8. **Recommendations for fixes**

This comprehensive test suite ensures that all claimed features actually work as advertised!
