/**
 * Test Scrolling Fix
 * Run this in the popup console to test the improved scrolling functionality
 */

async function testScrollingFix() {
  console.log('📜 Testing Scrolling Fix...');
  
  try {
    // Test 1: Check if scroll elements exist
    console.log('1. Checking scroll elements...');
    
    const chatMessages = document.getElementById('chatMessages');
    const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');
    
    if (chatMessages) {
      console.log('✅ Chat messages container found');
      console.log('Chat messages height:', chatMessages.scrollHeight);
      console.log('Chat messages visible height:', chatMessages.clientHeight);
      console.log('Current scroll position:', chatMessages.scrollTop);
    } else {
      console.error('❌ Chat messages container not found');
      return false;
    }
    
    if (scrollToBottomBtn) {
      console.log('✅ Scroll to bottom button found');
      console.log('Button display:', scrollToBottomBtn.style.display);
    } else {
      console.error('❌ Scroll to bottom button not found');
    }
    
    // Test 2: Add multiple test messages to create scrollable content
    console.log('\n2. Adding test messages to create scrollable content...');
    
    if (window.popupManager) {
      // Add several test messages
      for (let i = 1; i <= 10; i++) {
        window.popupManager.addMessageToChat('user', `Test user message ${i} - This is a longer message to create more content that will require scrolling to view properly.`);
        window.popupManager.addMessageToChat('ai', `Test AI response ${i} - This is a detailed response that explains various concepts and provides comprehensive information that takes up multiple lines and creates the need for scrolling in the chat interface. This helps test the scrolling functionality thoroughly.`);
      }
      
      console.log('✅ Added 20 test messages');
      
      // Check scroll state after adding messages
      setTimeout(() => {
        console.log('Updated scroll height:', chatMessages.scrollHeight);
        console.log('Updated scroll position:', chatMessages.scrollTop);
        console.log('Is scrolled to bottom:', chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10);
      }, 500);
      
    } else {
      console.error('❌ PopupManager not found');
      return false;
    }
    
    // Test 3: Test scroll to bottom functionality
    console.log('\n3. Testing scroll to bottom functionality...');
    
    setTimeout(() => {
      // Scroll to top first
      chatMessages.scrollTop = 0;
      console.log('Scrolled to top, position:', chatMessages.scrollTop);
      
      setTimeout(() => {
        // Check if scroll to bottom button appears
        const buttonVisible = scrollToBottomBtn.style.display !== 'none';
        console.log('Scroll to bottom button visible:', buttonVisible);
        
        if (buttonVisible) {
          console.log('✅ Button appears when not at bottom');
          
          // Test clicking the button
          scrollToBottomBtn.click();
          
          setTimeout(() => {
            const isAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;
            console.log('After clicking button, at bottom:', isAtBottom);
            
            if (isAtBottom) {
              console.log('✅ Scroll to bottom button works correctly');
            } else {
              console.error('❌ Scroll to bottom button not working');
            }
          }, 1000);
          
        } else {
          console.error('❌ Button not appearing when scrolled up');
        }
      }, 500);
    }, 1000);
    
    // Test 4: Test auto-scroll on new messages
    console.log('\n4. Testing auto-scroll on new messages...');
    
    setTimeout(() => {
      // Ensure we're at bottom
      if (window.popupManager) {
        window.popupManager.scrollToBottom();
        
        setTimeout(() => {
          // Add a new message
          window.popupManager.addMessageToChat('ai', 'This is a new message that should auto-scroll to bottom');
          
          setTimeout(() => {
            const isAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;
            console.log('After new message, still at bottom:', isAtBottom);
            
            if (isAtBottom) {
              console.log('✅ Auto-scroll on new messages works');
            } else {
              console.error('❌ Auto-scroll on new messages not working');
            }
          }, 500);
        }, 500);
      }
    }, 3000);
    
    return true;
    
  } catch (error) {
    console.error('❌ Scrolling test failed:', error);
    return false;
  }
}

// Function to test scroll behavior manually
function testScrollBehavior() {
  console.log('\n🧪 Manual Scroll Test Instructions:');
  console.log('1. Look at the chat messages area');
  console.log('2. If there are many messages, scroll up manually');
  console.log('3. You should see a blue ↓ button appear in bottom-right');
  console.log('4. Click the ↓ button to scroll back to bottom');
  console.log('5. The button should disappear when at bottom');
  console.log('6. Send a new message - should auto-scroll to show it');
}

// Function to clear test messages
function clearTestMessages() {
  console.log('🧹 Clearing test messages...');
  
  if (window.popupManager) {
    window.popupManager.clearCurrentSession();
    console.log('✅ Test messages cleared');
  } else {
    console.error('❌ PopupManager not found');
  }
}

// Function to check current scroll state
function checkScrollState() {
  console.log('\n📊 Current Scroll State:');
  
  const chatMessages = document.getElementById('chatMessages');
  const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');
  
  if (chatMessages) {
    const { scrollTop, scrollHeight, clientHeight } = chatMessages;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
    
    console.log('Scroll height:', scrollHeight);
    console.log('Client height:', clientHeight);
    console.log('Scroll position:', scrollTop);
    console.log('Is at bottom:', isAtBottom);
    console.log('Scroll percentage:', Math.round((scrollTop / (scrollHeight - clientHeight)) * 100) + '%');
  }
  
  if (scrollToBottomBtn) {
    console.log('Scroll button visible:', scrollToBottomBtn.style.display !== 'none');
  }
}

// Auto-run the test
testScrollingFix().then(success => {
  console.log('\n📊 SCROLLING TEST RESULT');
  console.log('=========================');
  
  if (success) {
    console.log('✅ SCROLLING IMPROVEMENTS WORKING!');
    console.log('✅ Auto-scroll to bottom on new messages');
    console.log('✅ Scroll to bottom button appears/disappears correctly');
    console.log('✅ Smooth scrolling behavior implemented');
    console.log('✅ Better scrollbar styling applied');
    
    testScrollBehavior();
  } else {
    console.log('❌ SCROLLING ISSUES DETECTED');
    console.log('❌ Check console for specific errors');
  }
});

// Export functions for manual use
window.testScrollingFix = testScrollingFix;
window.testScrollBehavior = testScrollBehavior;
window.clearTestMessages = clearTestMessages;
window.checkScrollState = checkScrollState;
