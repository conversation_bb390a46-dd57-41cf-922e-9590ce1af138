<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal AI Mentor - Settings</title>
    <link rel="stylesheet" href="settings.css">
</head>
<body>
    <div class="settings-container">
        <!-- Header -->
        <header class="settings-header">
            <div class="header-content">
                <h1>Universal AI Mentor Settings</h1>
                <p>Configure your AI provider and customize your experience</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="settings-main">
            <!-- API Configuration Section -->
            <section class="settings-section">
                <h2>API Configuration</h2>
                <p class="section-description">Choose your AI provider and configure API access</p>
                
                <div class="form-group">
                    <label for="providerSelect">AI Provider</label>
                    <select id="providerSelect" class="form-control">
                        <option value="">Select a provider...</option>
                        <option value="openai">OpenAI (GPT-4, GPT-3.5)</option>
                        <option value="anthropic">Anthropic (Claude)</option>
                        <option value="google">Google (Gemini)</option>
                        <option value="groq">GroqCloud (Llama, Gemma, Ultra-Fast)</option>
                        <option value="custom">Custom API (OpenAI-compatible)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="apiKeyInput">API Key</label>
                    <div class="input-with-toggle">
                        <input type="password" id="apiKeyInput" class="form-control" placeholder="Enter your API key">
                        <button type="button" id="toggleApiKey" class="toggle-btn" title="Show/Hide API Key">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    <small class="form-help">Your API key is stored securely and never shared</small>
                </div>

                <div class="form-group">
                    <label for="modelSelect">Model</label>
                    <select id="modelSelect" class="form-control">
                        <option value="">Select a model...</option>
                    </select>
                    <small class="form-help">Choose the AI model to use for responses</small>
                </div>

                <!-- Custom API Fields -->
                <div id="customApiFields" class="custom-api-fields" style="display: none;">
                    <div class="form-group">
                        <label for="customBaseUrl">Custom Base URL</label>
                        <input type="url" id="customBaseUrl" class="form-control" placeholder="https://api.example.com/v1">
                        <small class="form-help">Base URL for your custom OpenAI-compatible API</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="customModel">Custom Model Name</label>
                        <input type="text" id="customModel" class="form-control" placeholder="gpt-3.5-turbo">
                        <small class="form-help">Model name as expected by your API</small>
                    </div>
                </div>

                <!-- API Test -->
                <div class="api-test-section">
                    <button id="testApiBtn" class="test-btn" disabled>
                        <span class="btn-text">Test API Connection</span>
                        <span class="btn-spinner" style="display: none;">Testing...</span>
                    </button>
                    <div id="testResult" class="test-result"></div>
                </div>
            </section>

            <!-- Advanced Settings Section -->
            <section class="settings-section">
                <h2>Advanced Settings</h2>
                <p class="section-description">Fine-tune AI behavior and response parameters</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="maxTokensInfo">Response Length</label>
                        <div class="info-display" id="maxTokensInfo">
                            <span class="info-value">Maximum for selected model</span>
                            <span class="info-description">Automatically optimized</span>
                        </div>
                        <small class="form-help">Response length is automatically set to the maximum supported by your selected model for best results</small>
                    </div>

                    <div class="form-group">
                        <label for="temperatureInput">Temperature</label>
                        <input type="range" id="temperatureInput" class="form-range" min="0" max="1" step="0.1" value="0.7">
                        <div class="range-labels">
                            <span>Focused (0)</span>
                            <span id="temperatureValue">0.7</span>
                            <span>Creative (1)</span>
                        </div>
                        <small class="form-help">Controls randomness in responses</small>
                    </div>
                </div>
            </section>

            <!-- User Preferences Section -->
            <section class="settings-section">
                <h2>User Preferences</h2>
                <p class="section-description">Customize your extension experience</p>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showContextMenu" checked>
                            <span class="checkmark"></span>
                            Show context menu for selected text
                        </label>
                        <small class="form-help">Right-click on selected text to ask AI</small>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSelectText" checked>
                            <span class="checkmark"></span>
                            Auto-detect selected text in popup
                        </label>
                        <small class="form-help">Automatically use selected text when opening popup</small>
                    </div>
                </div>

                <div class="form-group">
                    <label for="themeSelect">Theme</label>
                    <select id="themeSelect" class="form-control">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto (System)</option>
                    </select>
                </div>
            </section>

            <!-- Data Management Section -->
            <section class="settings-section">
                <h2>Data Management</h2>
                <p class="section-description">Manage your conversation history and data</p>
                
                <div class="data-actions">
                    <button id="exportDataBtn" class="secondary-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Export Data
                    </button>
                    
                    <button id="clearHistoryBtn" class="danger-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                        </svg>
                        Clear History
                    </button>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="settings-footer">
            <div class="footer-actions">
                <button id="resetBtn" class="secondary-btn">Reset to Defaults</button>
                <div class="primary-actions">
                    <button id="cancelBtn" class="secondary-btn">Cancel</button>
                    <button id="saveBtn" class="primary-btn">Save Settings</button>
                </div>
            </div>
        </footer>

        <!-- Status Messages -->
        <div id="statusMessage" class="status-message" style="display: none;"></div>
    </div>

    <script src="../lib/llm-api.js"></script>
    <script src="../lib/storage.js"></script>
    <script src="settings.js"></script>
</body>
</html>
