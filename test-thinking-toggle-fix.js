/**
 * Test Thinking Toggle Fix
 * Run this in the popup console to test the fixed thinking toggle functionality
 */

function testThinkingToggleFix() {
  console.log('🧠 Testing Thinking Toggle Fix...');
  
  try {
    // Test response with thinking section
    const testResponse = `<think>
This is a test thinking section. The user is asking about frameworks, so I should provide a comprehensive answer with examples. Let me structure this properly with code examples and clear explanations.
</think>

Here are some easy-to-learn frameworks compared to React:

## 1. Vue.js
Vue.js is often considered more beginner-friendly than React because:
- **Simpler syntax** with template-based approach
- **Gradual learning curve** - you can start small and add features
- **Excellent documentation** and community support

**Example:**
\`\`\`html
<div id="app">
  <h1>{{ message }}</h1>
  <button @click="count++">Count: {{ count }}</button>
</div>
\`\`\`

\`\`\`javascript
new Vue({
  el: '#app',
  data: {
    message: 'Hello Vue!',
    count: 0
  }
})
\`\`\`

## 2. Alpine.js
Alpine.js is perfect for adding **reactivity** without a build step:
- Lightweight (~30KB)
- No compilation required
- Works with existing HTML

**Example:**
\`\`\`html
<div x-data="{ open: false }">
  <button @click="open = !open">Toggle</button>
  <div x-show="open">Content!</div>
</div>
\`\`\`

These frameworks are great alternatives to React for beginners!`;

    if (window.popupManager) {
      console.log('✅ PopupManager found, adding test response...');
      
      // Add the test response
      window.popupManager.addMessageToChat('ai', testResponse, 'Test Provider');
      
      console.log('✅ Test response added');
      
      // Wait for DOM to update, then test
      setTimeout(() => {
        console.log('\n🔍 Checking for thinking sections...');
        
        const thinkingSections = document.querySelectorAll('.thinking-section');
        const thinkingToggles = document.querySelectorAll('.thinking-toggle');
        const thinkingContents = document.querySelectorAll('.thinking-content');
        const codeBlocks = document.querySelectorAll('.code-block-container');
        
        console.log('Thinking sections found:', thinkingSections.length);
        console.log('Thinking toggles found:', thinkingToggles.length);
        console.log('Thinking contents found:', thinkingContents.length);
        console.log('Code blocks found:', codeBlocks.length);
        
        if (thinkingSections.length > 0) {
          console.log('✅ Thinking sections properly created');
          
          // Test the toggle functionality
          if (thinkingToggles.length > 0) {
            console.log('\n🧪 Testing toggle functionality...');
            
            const firstToggle = thinkingToggles[0];
            const firstContent = thinkingContents[0];
            
            console.log('Initial content display:', firstContent.style.display);
            
            // Test clicking the toggle
            console.log('🖱️ Clicking toggle button...');
            firstToggle.click();
            
            setTimeout(() => {
              console.log('After click, content display:', firstContent.style.display);
              
              if (firstContent.style.display === 'block') {
                console.log('✅ Toggle works - content is now visible');
                
                // Test clicking again to hide
                console.log('🖱️ Clicking toggle again to hide...');
                firstToggle.click();
                
                setTimeout(() => {
                  console.log('After second click, content display:', firstContent.style.display);
                  
                  if (firstContent.style.display === 'none') {
                    console.log('✅ Toggle works both ways - content hidden again');
                  } else {
                    console.log('❌ Toggle not working to hide content');
                  }
                }, 100);
                
              } else {
                console.log('❌ Toggle not working - content still hidden');
              }
            }, 100);
            
          } else {
            console.log('❌ No thinking toggles found');
          }
          
        } else {
          console.log('❌ No thinking sections found');
        }
        
        // Test code blocks
        if (codeBlocks.length > 0) {
          console.log('\n✅ Code blocks properly formatted');
          
          const copyButtons = document.querySelectorAll('.copy-code-btn');
          console.log('Copy buttons found:', copyButtons.length);
          
          if (copyButtons.length > 0) {
            console.log('✅ Copy buttons added to code blocks');
          }
        } else {
          console.log('❌ Code blocks not properly formatted');
        }
        
        // Check if main answer is visible
        console.log('\n📄 Checking main answer visibility...');
        const messageAI = document.querySelector('.message-ai:last-child');
        if (messageAI) {
          const textContent = messageAI.textContent;
          if (textContent.includes('Vue.js') && textContent.includes('Alpine.js')) {
            console.log('✅ Main answer is visible and properly formatted');
          } else {
            console.log('❌ Main answer content not visible');
          }
        }
        
      }, 500);
      
    } else {
      console.error('❌ PopupManager not found');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Thinking toggle test failed:', error);
    return false;
  }
}

// Function to manually test toggle
function manualToggleTest() {
  console.log('\n🔧 Manual Toggle Test');
  console.log('===================');
  
  const thinkingToggles = document.querySelectorAll('.thinking-toggle');
  
  if (thinkingToggles.length > 0) {
    console.log(`Found ${thinkingToggles.length} thinking toggle(s)`);
    console.log('Click any "🧠 AI Thinking Process" button to test');
    
    thinkingToggles.forEach((toggle, index) => {
      console.log(`Toggle ${index + 1}:`, toggle);
    });
  } else {
    console.log('❌ No thinking toggles found');
    console.log('💡 Try adding a message with <think> tags first');
  }
}

// Function to check current state
function checkCurrentState() {
  console.log('\n📊 Current State Check');
  console.log('=====================');
  
  const thinkingSections = document.querySelectorAll('.thinking-section');
  const thinkingContents = document.querySelectorAll('.thinking-content');
  const codeBlocks = document.querySelectorAll('.code-block-container');
  
  console.log('Thinking sections:', thinkingSections.length);
  console.log('Thinking contents:', thinkingContents.length);
  console.log('Code blocks:', codeBlocks.length);
  
  thinkingContents.forEach((content, index) => {
    console.log(`Thinking content ${index + 1} display:`, content.style.display);
  });
}

// Auto-run the test
testThinkingToggleFix().then(success => {
  console.log('\n📊 THINKING TOGGLE FIX TEST RESULT');
  console.log('===================================');
  
  if (success) {
    console.log('✅ THINKING TOGGLE FIX WORKING!');
    console.log('✅ Thinking sections are properly formatted');
    console.log('✅ Toggle buttons are functional');
    console.log('✅ Main answer is visible');
    console.log('✅ Code blocks have copy buttons');
    
    console.log('\n🧪 Additional Tests Available:');
    console.log('- manualToggleTest() - Check available toggles');
    console.log('- checkCurrentState() - View current state');
  } else {
    console.log('❌ THINKING TOGGLE ISSUES DETECTED');
    console.log('❌ Check console for specific errors');
  }
});

// Export functions for manual use
window.testThinkingToggleFix = testThinkingToggleFix;
window.manualToggleTest = manualToggleTest;
window.checkCurrentState = checkCurrentState;
