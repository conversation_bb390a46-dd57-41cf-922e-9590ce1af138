<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Extension Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            border-radius: 8px;
            margin-bottom: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .icon-16 { width: 16px; height: 16px; }
        .icon-32 { width: 32px; height: 32px; }
        .icon-48 { width: 48px; height: 48px; }
        .icon-128 { width: 128px; height: 128px; }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h1>Universal AI Mentor - Icon Generator</h1>
        <p>This page helps you generate the required icons for the browser extension. You can:</p>
        <ol>
            <li>Take screenshots of the SVG icons below</li>
            <li>Use an online tool to convert them to PNG</li>
            <li>Save them with the correct names in the icons/ directory</li>
        </ol>
        <p><strong>Required files:</strong> icon16.png, icon32.png, icon48.png, icon128.png</p>
    </div>

    <div class="icon-container">
        <svg class="icon icon-16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad16" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="24" height="24" rx="4" fill="url(#grad16)"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="white"/>
        </svg>
        <div>16x16</div>
        <button class="download-btn" onclick="downloadIcon(16)">Download</button>
    </div>

    <div class="icon-container">
        <svg class="icon icon-32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad32" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="24" height="24" rx="4" fill="url(#grad32)"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="white"/>
        </svg>
        <div>32x32</div>
        <button class="download-btn" onclick="downloadIcon(32)">Download</button>
    </div>

    <div class="icon-container">
        <svg class="icon icon-48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad48" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="24" height="24" rx="4" fill="url(#grad48)"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="white"/>
        </svg>
        <div>48x48</div>
        <button class="download-btn" onclick="downloadIcon(48)">Download</button>
    </div>

    <div class="icon-container">
        <svg class="icon icon-128" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad128" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="24" height="24" rx="4" fill="url(#grad128)"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="white"/>
        </svg>
        <div>128x128</div>
        <button class="download-btn" onclick="downloadIcon(128)">Download</button>
    </div>

    <div class="instructions">
        <h2>Alternative: Use Online Icon Generators</h2>
        <p>You can also use these online tools to create professional icons:</p>
        <ul>
            <li><a href="https://favicon.io/" target="_blank">Favicon.io</a> - Generate from text or image</li>
            <li><a href="https://realfavicongenerator.net/" target="_blank">RealFaviconGenerator</a> - Comprehensive icon generator</li>
            <li><a href="https://www.canva.com/" target="_blank">Canva</a> - Design custom icons</li>
        </ul>
        
        <h3>Icon Design Guidelines</h3>
        <ul>
            <li>Use the brand colors: Primary #667eea, Secondary #764ba2</li>
            <li>Keep the design simple and recognizable at small sizes</li>
            <li>Consider using the AI/brain/chat bubble theme</li>
            <li>Ensure good contrast for visibility</li>
        </ul>
    </div>

    <script>
        function downloadIcon(size) {
            const svg = document.querySelector(`.icon-${size}`);
            const svgData = new XMLSerializer().serializeToString(svg);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            canvas.width = size;
            canvas.height = size;
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            };
            
            img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
        }
    </script>
</body>
</html>
