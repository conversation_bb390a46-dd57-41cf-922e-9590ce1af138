/**
 * Test Context Menu
 * Run this in the popup console to test context menu functionality
 */

async function testContextMenu() {
  console.log('🖱️ Testing Context Menu Functionality...');
  
  try {
    // Test 1: Check if context menu is created
    console.log('1. Checking if context menu exists...');
    
    // We can't directly query context menus from content script, but we can check background console
    console.log('📋 To check context menu creation:');
    console.log('1. Go to chrome://extensions/');
    console.log('2. Click "Inspect views: service worker" for Universal AI Mentor');
    console.log('3. Look for these messages in the console:');
    console.log('   - "🔧 Setting up context menu..."');
    console.log('   - "✅ Context menu created successfully"');
    console.log('   - "🔧 Setting up context menu click handler..."');
    
    // Test 2: Check preferences
    console.log('\n2. Checking context menu preferences...');
    
    try {
      const prefsResponse = await chrome.runtime.sendMessage({ type: 'GET_PREFERENCES' });
      
      if (prefsResponse && prefsResponse.success) {
        console.log('Preferences:', prefsResponse.preferences);
        
        if (prefsResponse.preferences.showContextMenu) {
          console.log('✅ Context menu is enabled in preferences');
        } else {
          console.log('❌ Context menu is disabled in preferences');
          console.log('💡 You can enable it in the settings page');
        }
      } else {
        console.error('❌ Failed to get preferences');
      }
    } catch (error) {
      console.error('❌ Preferences check failed:', error);
    }
    
    // Test 3: Instructions for manual testing
    console.log('\n3. Manual context menu test instructions:');
    console.log('📋 To test the context menu:');
    console.log('1. Go to any webpage (like Wikipedia or a news site)');
    console.log('2. Select some text with your mouse');
    console.log('3. Right-click on the selected text');
    console.log('4. Look for "Ask AI Mentor about [selected text]" in the context menu');
    console.log('5. Click it if you see it');
    console.log('');
    console.log('If you don\'t see the context menu item:');
    console.log('- Check the background console for error messages');
    console.log('- Make sure you\'re on an http:// or https:// page');
    console.log('- Try reloading the extension');
    
    // Test 4: Check if content script is loaded
    console.log('\n4. Testing content script availability...');
    
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.url && (tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
        console.log('✅ Current tab is a valid web page:', tab.url);
        
        try {
          const response = await chrome.tabs.sendMessage(tab.id, { type: 'PING' });
          console.log('✅ Content script is loaded and responding');
        } catch (error) {
          console.log('⚠️ Content script not responding (this is normal for some pages)');
        }
      } else {
        console.log('⚠️ Current tab is not a web page (extension pages, chrome:// etc.)');
        console.log('Context menu only works on regular web pages');
      }
    } catch (error) {
      console.error('❌ Tab check failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Context menu test failed:', error);
  }
}

// Function to check background console specifically for context menu
function checkBackgroundForContextMenu() {
  console.log('\n📋 Background Console Check for Context Menu:');
  console.log('1. Go to chrome://extensions/');
  console.log('2. Find "Universal AI Mentor"');
  console.log('3. Click "Inspect views: service worker"');
  console.log('4. Look for these specific messages:');
  console.log('');
  console.log('SUCCESS messages to look for:');
  console.log('   ✅ "🔧 Setting up context menu..."');
  console.log('   ✅ "✅ Context menu created successfully"');
  console.log('   ✅ "🔧 Setting up context menu click handler..."');
  console.log('');
  console.log('ERROR messages that indicate problems:');
  console.log('   ❌ "❌ Failed to setup context menu:"');
  console.log('   ❌ "❌ StorageManager not ready"');
  console.log('   ❌ Any other error messages');
  console.log('');
  console.log('When you right-click on selected text, you should see:');
  console.log('   ✅ "🖱️ Context menu clicked:"');
  console.log('   ✅ "✅ Valid context menu click with selected text"');
}

// Function to simulate context menu test
function simulateContextMenuTest() {
  console.log('\n🧪 Context Menu Test Simulation:');
  console.log('Since we can\'t directly test context menu from popup, here\'s what should happen:');
  console.log('');
  console.log('1. User selects text: "quantum computing"');
  console.log('2. User right-clicks');
  console.log('3. Context menu shows: "Ask AI Mentor about quantum computing"');
  console.log('4. User clicks the menu item');
  console.log('5. Background script receives the click');
  console.log('6. AI processes the request');
  console.log('7. Response appears on the page');
  console.log('');
  console.log('If this isn\'t working, the issue is likely:');
  console.log('- Context menu not created (check background console)');
  console.log('- Content script not loaded (try refreshing the page)');
  console.log('- Permissions issue (check manifest.json)');
}

// Auto-run the test
testContextMenu();

// Export functions for manual use
window.testContextMenu = testContextMenu;
window.checkBackgroundForContextMenu = checkBackgroundForContextMenu;
window.simulateContextMenuTest = simulateContextMenuTest;
