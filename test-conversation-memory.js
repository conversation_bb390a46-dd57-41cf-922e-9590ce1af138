/**
 * Test Conversation Memory Implementation
 * Run this in the popup console to test conversation context
 */

function testConversationMemory() {
  console.log('🧠 Testing Conversation Memory...');
  
  try {
    // Test 1: Check if <PERSON>upManager has conversation tracking
    if (window.popupManager) {
      console.log('✅ PopupManager found');
      
      const sessionMessages = window.popupManager.currentSessionMessages;
      console.log('Current session messages:', sessionMessages.length);
      
      if (sessionMessages.length > 0) {
        console.log('📝 Session messages:');
        sessionMessages.forEach((msg, index) => {
          console.log(`  ${index + 1}. ${msg.type}: ${msg.content.substring(0, 50)}...`);
        });
      } else {
        console.log('ℹ️ No messages in current session');
      }
      
    } else {
      console.error('❌ PopupManager not found');
      return false;
    }
    
    // Test 2: Check if LLMAPIClient supports conversation history
    if (typeof LLMAPIClient !== 'undefined') {
      console.log('\n🔧 Testing LLMAPIClient conversation support...');
      
      const client = new LLMAPIClient();
      
      // Test buildConversationMessages function
      const testHistory = [
        { type: 'user', content: 'What is Python?' },
        { type: 'ai', content: 'Python is a programming language...' },
        { type: 'user', content: 'Can you show me a simple example?' }
      ];
      
      const messages = client.buildConversationMessages(
        'Write a hello world program',
        '',
        testHistory
      );
      
      console.log('Built conversation messages:', messages.length);
      console.log('Messages structure:');
      messages.forEach((msg, index) => {
        console.log(`  ${index + 1}. ${msg.role}: ${msg.content.substring(0, 50)}...`);
      });
      
      if (messages.length === 5) { // system + 3 history + 1 current
        console.log('✅ Conversation messages built correctly');
      } else {
        console.log('❌ Unexpected number of messages');
      }
      
    } else {
      console.log('❌ LLMAPIClient not available');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Conversation memory test failed:', error);
    return false;
  }
}

// Function to simulate a conversation
function simulateConversation() {
  console.log('\n🎭 Simulating Conversation Flow...');
  
  if (!window.popupManager) {
    console.error('❌ PopupManager not available');
    return;
  }
  
  // Simulate adding messages to session
  const testMessages = [
    { type: 'user', content: 'What is the Tower of Hanoi problem?', timestamp: Date.now() - 60000 },
    { type: 'ai', content: 'The Tower of Hanoi is a classic puzzle...', provider: 'groq', timestamp: Date.now() - 50000 },
    { type: 'user', content: 'Can you write Python code for it?', timestamp: Date.now() - 40000 },
    { type: 'ai', content: 'Here\'s a Python implementation...', provider: 'groq', timestamp: Date.now() - 30000 }
  ];
  
  // Clear current session first
  window.popupManager.currentSessionMessages = [];
  
  // Add test messages
  testMessages.forEach(msg => {
    window.popupManager.currentSessionMessages.push(msg);
  });
  
  console.log('📝 Added test conversation with', testMessages.length, 'messages');
  
  // Now test what would be sent to API
  if (typeof LLMAPIClient !== 'undefined') {
    const client = new LLMAPIClient();
    const messages = client.buildConversationMessages(
      'Can you make it more efficient?',
      '',
      window.popupManager.currentSessionMessages
    );
    
    console.log('\n💬 API would receive:');
    messages.forEach((msg, index) => {
      console.log(`${index + 1}. ${msg.role}: ${msg.content.substring(0, 80)}...`);
    });
    
    console.log('\n✅ Conversation context would be maintained!');
    console.log('✅ AI would remember previous Tower of Hanoi discussion');
    console.log('✅ AI would know we already discussed Python implementation');
  }
}

// Function to test conversation persistence
function testConversationPersistence() {
  console.log('\n💾 Testing Conversation Persistence...');
  
  if (!window.popupManager) {
    console.error('❌ PopupManager not available');
    return;
  }
  
  // Check if session is saved to storage
  chrome.storage.local.get(['currentSession'], (result) => {
    const session = result.currentSession;
    
    if (session && session.messages) {
      console.log('✅ Session found in storage');
      console.log('Messages in storage:', session.messages.length);
      console.log('Session timestamp:', new Date(session.timestamp));
      
      // Check if session is recent (within 1 hour)
      const oneHour = 60 * 60 * 1000;
      const isRecent = Date.now() - session.timestamp < oneHour;
      
      console.log('Session is recent:', isRecent);
      
      if (isRecent) {
        console.log('✅ Session would be restored on popup reopen');
      } else {
        console.log('⚠️ Session is old and would be cleared');
      }
      
    } else {
      console.log('ℹ️ No session found in storage');
    }
  });
}

// Function to check conversation context in API calls
function checkAPIContextSupport() {
  console.log('\n🔌 Checking API Context Support...');
  
  const providers = ['openai', 'anthropic', 'groq', 'custom'];
  
  providers.forEach(provider => {
    console.log(`\n${provider.toUpperCase()} API:`);
    
    switch (provider) {
      case 'openai':
      case 'groq':
      case 'custom':
        console.log('✅ Supports conversation messages array');
        console.log('✅ System message + conversation history');
        break;
        
      case 'anthropic':
        console.log('✅ Supports conversation messages');
        console.log('✅ System message as separate parameter');
        break;
        
      case 'google':
        console.log('✅ Converted to Google format');
        console.log('✅ Role mapping (assistant -> model)');
        break;
    }
  });
}

// Function to demonstrate the fix
function demonstrateMemoryFix() {
  console.log('\n🎯 CONVERSATION MEMORY FIX DEMONSTRATION');
  console.log('=========================================');
  
  console.log('\n❌ BEFORE (No Memory):');
  console.log('User: "What is Tower of Hanoi?"');
  console.log('AI: "Tower of Hanoi is a puzzle..."');
  console.log('User: "Write Python code for it"');
  console.log('AI: "What problem do you want Python code for?" ← NO MEMORY!');
  
  console.log('\n✅ AFTER (With Memory):');
  console.log('User: "What is Tower of Hanoi?"');
  console.log('AI: "Tower of Hanoi is a puzzle..."');
  console.log('User: "Write Python code for it"');
  console.log('AI: "Here\'s Python code for the Tower of Hanoi:" ← REMEMBERS!');
  
  console.log('\n🧠 How it works:');
  console.log('1. Each message is stored in currentSessionMessages[]');
  console.log('2. When sending new message, entire history is included');
  console.log('3. AI receives full conversation context');
  console.log('4. AI can reference previous messages');
  console.log('5. Session persists for 1 hour even if popup closes');
}

// Auto-run the test
testConversationMemory().then(success => {
  console.log('\n📊 CONVERSATION MEMORY TEST RESULT');
  console.log('===================================');
  
  if (success) {
    console.log('✅ CONVERSATION MEMORY IMPLEMENTED!');
    console.log('✅ Session messages tracked');
    console.log('✅ Conversation history sent to API');
    console.log('✅ Context maintained across messages');
    console.log('✅ All providers support conversation');
    
    console.log('\n🧪 Additional Tests Available:');
    console.log('- simulateConversation() - Test conversation flow');
    console.log('- testConversationPersistence() - Test storage');
    console.log('- checkAPIContextSupport() - Check provider support');
    console.log('- demonstrateMemoryFix() - See before/after');
  } else {
    console.log('❌ CONVERSATION MEMORY ISSUES DETECTED');
    console.log('❌ Check console for specific errors');
  }
});

// Export functions for manual use
window.testConversationMemory = testConversationMemory;
window.simulateConversation = simulateConversation;
window.testConversationPersistence = testConversationPersistence;
window.checkAPIContextSupport = checkAPIContextSupport;
window.demonstrateMemoryFix = demonstrateMemoryFix;
