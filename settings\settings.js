/**
 * Settings Page Script
 * Handles configuration and preferences management
 */

class SettingsManager {
  constructor() {
    this.storageManager = new StorageManager();
    this.currentConfig = null;
    this.currentPreferences = null;

    this.init();
  }

  async init() {
    // Initialize DOM elements
    this.initializeElements();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Load current settings
    await this.loadSettings();
    
    // Update UI based on loaded settings
    this.updateUI();
  }

  initializeElements() {
    // API Configuration
    this.providerSelect = document.getElementById('providerSelect');
    this.apiKeyInput = document.getElementById('apiKeyInput');
    this.toggleApiKeyBtn = document.getElementById('toggleApiKey');
    this.modelSelect = document.getElementById('modelSelect');
    this.customApiFields = document.getElementById('customApiFields');
    this.customBaseUrl = document.getElementById('customBaseUrl');
    this.customModel = document.getElementById('customModel');
    this.testApiBtn = document.getElementById('testApiBtn');
    this.testResult = document.getElementById('testResult');
    
    // Advanced Settings
    this.maxTokensInfo = document.getElementById('maxTokensInfo');
    this.temperatureInput = document.getElementById('temperatureInput');
    this.temperatureValue = document.getElementById('temperatureValue');
    
    // User Preferences
    this.showContextMenu = document.getElementById('showContextMenu');
    this.autoSelectText = document.getElementById('autoSelectText');
    this.themeSelect = document.getElementById('themeSelect');
    
    // Actions
    this.exportDataBtn = document.getElementById('exportDataBtn');
    this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
    this.resetBtn = document.getElementById('resetBtn');
    this.cancelBtn = document.getElementById('cancelBtn');
    this.saveBtn = document.getElementById('saveBtn');
    
    // Status
    this.statusMessage = document.getElementById('statusMessage');

    // Model max tokens mapping
    this.modelMaxTokens = {
      // OpenAI Models
      'gpt-4o': 16384,
      'gpt-4o-mini': 16384,
      'gpt-4-turbo': 4096,
      'gpt-4': 8192,
      'gpt-3.5-turbo': 4096,

      // Anthropic Models
      'claude-3-5-sonnet-20241022': 8192,
      'claude-3-5-haiku-20241022': 8192,
      'claude-3-opus-20240229': 4096,
      'claude-3-sonnet-20240229': 4096,
      'claude-3-haiku-20240307': 4096,

      // Google Models
      'gemini-1.5-pro': 8192,
      'gemini-1.5-flash': 8192,
      'gemini-pro': 2048,
      'gemini-pro-vision': 2048,

      // Groq Models
      'llama-3.3-70b-versatile': 32768,
      'llama-3.1-70b-versatile': 32768,
      'llama-3.1-8b-instant': 32768,
      'llama3-70b-8192': 8192,
      'llama3-8b-8192': 8192,
      'mixtral-8x7b-32768': 32768,
      'gemma2-9b-it': 8192,
      'deepseek-r1-distill-llama-70b': 32768,
      'qwen-qwq-32b': 32768,
      'mistral-saba-24b': 24576,

      // Default fallback
      'default': 4096
    };
  }

  setupEventListeners() {
    // Provider selection
    this.providerSelect.addEventListener('change', () => {
      this.onProviderChange();
    });
    
    // API key toggle
    this.toggleApiKeyBtn.addEventListener('click', () => {
      this.toggleApiKeyVisibility();
    });
    
    // Form validation
    this.apiKeyInput.addEventListener('input', () => {
      this.validateForm();
    });
    
    this.customBaseUrl.addEventListener('input', () => {
      this.validateForm();
    });
    
    this.customModel.addEventListener('input', () => {
      this.validateForm();
    });
    
    // Model selection
    this.modelSelect.addEventListener('change', () => {
      this.updateMaxTokensDisplay();
    });

    // Temperature slider
    this.temperatureInput.addEventListener('input', () => {
      this.temperatureValue.textContent = this.temperatureInput.value;
    });
    
    // API test
    this.testApiBtn.addEventListener('click', () => {
      this.testApiConnection();
    });
    
    // Data management
    this.exportDataBtn.addEventListener('click', () => {
      this.exportData();
    });
    
    this.clearHistoryBtn.addEventListener('click', () => {
      this.clearHistory();
    });
    
    // Footer actions
    this.resetBtn.addEventListener('click', () => {
      this.resetToDefaults();
    });
    
    this.cancelBtn.addEventListener('click', () => {
      window.close();
    });
    
    this.saveBtn.addEventListener('click', () => {
      this.saveSettings();
    });
  }

  async loadSettings() {
    try {
      // Load API configuration
      this.currentConfig = await this.storageManager.getAPIConfig();
      
      // Load user preferences
      this.currentPreferences = await this.storageManager.getUserPreferences();
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.showStatusMessage('Failed to load settings', 'error');
    }
  }

  updateUI() {
    // Update API configuration
    if (this.currentConfig) {
      this.providerSelect.value = this.currentConfig.provider || '';
      this.apiKeyInput.value = this.currentConfig.apiKey || '';
      this.modelSelect.value = this.currentConfig.model || '';
      this.customBaseUrl.value = this.currentConfig.customBaseUrl || '';
      this.customModel.value = this.currentConfig.customModel || '';
      this.temperatureInput.value = this.currentConfig.temperature || 0.7;
    }
    
    // Update user preferences
    if (this.currentPreferences) {
      this.showContextMenu.checked = this.currentPreferences.showContextMenu;
      this.autoSelectText.checked = this.currentPreferences.autoSelectText;
      this.themeSelect.value = this.currentPreferences.theme || 'light';
    }
    
    // Update temperature display
    this.temperatureValue.textContent = this.temperatureInput.value;
    
    // Update provider-specific UI
    this.onProviderChange();
    
    // Validate form
    this.validateForm();
  }

  onProviderChange() {
    const provider = this.providerSelect.value;
    
    // Show/hide custom API fields
    this.customApiFields.style.display = provider === 'custom' ? 'block' : 'none';
    
    // Update model options
    this.updateModelOptions(provider);

    // Update max tokens display
    this.updateMaxTokensDisplay();

    // Validate form
    this.validateForm();
  }

  updateModelOptions(provider) {
    // Clear existing options
    this.modelSelect.innerHTML = '<option value="">Select a model...</option>';

    // Define supported models for each provider
    const providerModels = {
      'openai': {
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        defaultModel: 'gpt-3.5-turbo'
      },
      'anthropic': {
        models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
        defaultModel: 'claude-3-sonnet-20240229'
      },
      'google': {
        models: ['gemini-pro', 'gemini-pro-vision'],
        defaultModel: 'gemini-pro'
      },
      'groq': {
        models: [
          'llama-3.3-70b-versatile',
          'llama-3.1-8b-instant',
          'gemma2-9b-it',
          'deepseek-r1-distill-llama-70b',
          'qwen-qwq-32b',
          'mistral-saba-24b'
        ],
        defaultModel: 'llama-3.3-70b-versatile'
      }
    };

    if (provider && provider !== 'custom' && providerModels[provider]) {
      const providerInfo = providerModels[provider];
      providerInfo.models.forEach(model => {
        const option = document.createElement('option');
        option.value = model;
        option.textContent = model;
        this.modelSelect.appendChild(option);
      });

      // Set default model if available
      if (providerInfo.defaultModel) {
        this.modelSelect.value = providerInfo.defaultModel;
      }
    }
  }

  updateMaxTokensDisplay() {
    const selectedModel = this.modelSelect.value;
    const provider = this.providerSelect.value;

    if (selectedModel && this.modelMaxTokens[selectedModel]) {
      const maxTokens = this.modelMaxTokens[selectedModel];
      this.maxTokensInfo.querySelector('.info-value').textContent = `${maxTokens.toLocaleString()} tokens maximum`;
      this.maxTokensInfo.querySelector('.info-description').textContent = `Optimized for ${selectedModel}`;
    } else if (provider) {
      this.maxTokensInfo.querySelector('.info-value').textContent = 'Maximum for selected model';
      this.maxTokensInfo.querySelector('.info-description').textContent = 'Automatically optimized';
    } else {
      this.maxTokensInfo.querySelector('.info-value').textContent = 'Select a model to see limits';
      this.maxTokensInfo.querySelector('.info-description').textContent = 'Choose provider and model first';
    }
  }

  toggleApiKeyVisibility() {
    const isPassword = this.apiKeyInput.type === 'password';
    this.apiKeyInput.type = isPassword ? 'text' : 'password';
    
    // Update icon (you could change the SVG here)
    const icon = this.toggleApiKeyBtn.querySelector('svg');
    if (isPassword) {
      // Show "hide" icon
      icon.innerHTML = `
        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
      `;
    } else {
      // Show "show" icon
      icon.innerHTML = `
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
      `;
    }
  }

  validateForm() {
    const provider = this.providerSelect.value;
    const apiKey = this.apiKeyInput.value.trim();
    const customBaseUrl = this.customBaseUrl.value.trim();
    const customModel = this.customModel.value.trim();
    
    let isValid = true;
    
    // Check required fields
    if (!provider) {
      isValid = false;
    }
    
    if (!apiKey) {
      isValid = false;
    }
    
    if (provider === 'custom') {
      if (!customBaseUrl || !customModel) {
        isValid = false;
      }
    }
    
    // Update test button state
    this.testApiBtn.disabled = !isValid;
    
    // Update save button state
    this.saveBtn.disabled = !isValid;
    
    return isValid;
  }

  async testApiConnection() {
    if (!this.validateForm()) {
      return;
    }

    // Show loading state
    this.testApiBtn.disabled = true;
    this.testApiBtn.querySelector('.btn-text').style.display = 'none';
    this.testApiBtn.querySelector('.btn-spinner').style.display = 'inline-block';
    this.testResult.style.display = 'none';

    try {
      // Create test configuration
      const testConfig = this.getFormConfig();

      // Test the API by making a direct call (since we're in settings page, not background)
      const response = await this.testAPIDirectly(testConfig);

      // Show success
      this.showTestResult(true, 'API connection successful! Response: ' + response.substring(0, 100) + '...');

    } catch (error) {
      console.error('API test failed:', error);
      this.showTestResult(false, 'API test failed: ' + error.message);
    } finally {
      // Reset button state
      this.testApiBtn.disabled = false;
      this.testApiBtn.querySelector('.btn-text').style.display = 'inline';
      this.testApiBtn.querySelector('.btn-spinner').style.display = 'none';
    }
  }

  async testAPIDirectly(config) {
    const prompt = 'Hello! This is a test message. Please respond with "Test successful!"';

    switch (config.provider) {
      case 'openai':
        return await this.testOpenAI(prompt, config);
      case 'anthropic':
        return await this.testAnthropic(prompt, config);
      case 'google':
        return await this.testGoogle(prompt, config);
      case 'groq':
        return await this.testGroq(prompt, config);
      case 'custom':
        return await this.testCustomAPI(prompt, config);
      default:
        throw new Error(`Provider ${config.provider} not implemented`);
    }
  }

  async testOpenAI(prompt, config) {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: prompt }
        ],
        max_tokens: 50
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'OpenAI API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async testAnthropic(prompt, config) {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: 50,
        messages: [{ role: 'user', content: prompt }]
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Anthropic API request failed');
    }

    const data = await response.json();
    return data.content[0].text;
  }

  async testGoogle(prompt, config) {
    const model = config.model || 'gemini-pro';
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }]
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Google API request failed');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  async testGroq(prompt, config) {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || 'llama-3.3-70b-versatile',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: prompt }
        ],
        max_tokens: 50
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'GroqCloud API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async testCustomAPI(prompt, config) {
    const response = await fetch(`${config.customBaseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.customModel,
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 50
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Custom API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  showTestResult(success, message) {
    this.testResult.className = `test-result ${success ? 'success' : 'error'}`;
    this.testResult.textContent = message;
    this.testResult.style.display = 'block';
  }

  getFormConfig() {
    return {
      provider: this.providerSelect.value,
      apiKey: this.apiKeyInput.value.trim(),
      model: this.modelSelect.value || undefined,
      customBaseUrl: this.customBaseUrl.value.trim() || undefined,
      customModel: this.customModel.value.trim() || undefined,
      temperature: parseFloat(this.temperatureInput.value) || 0.7
    };
  }

  getFormPreferences() {
    return {
      showContextMenu: this.showContextMenu.checked,
      autoSelectText: this.autoSelectText.checked,
      theme: this.themeSelect.value,
      temperature: parseFloat(this.temperatureInput.value) || 0.7
    };
  }

  async saveSettings() {
    if (!this.validateForm()) {
      this.showStatusMessage('Please fill in all required fields', 'error');
      return;
    }
    
    try {
      // Get form data
      const config = this.getFormConfig();
      const preferences = this.getFormPreferences();
      
      // Validate configuration
      const validation = this.storageManager.validateAPIConfig(config);
      if (!validation.isValid) {
        this.showStatusMessage('Configuration error: ' + validation.errors.join(', '), 'error');
        return;
      }
      
      // Save to storage
      const [configSaved, prefsSaved] = await Promise.all([
        this.storageManager.saveAPIConfig(config),
        this.storageManager.saveUserPreferences(preferences)
      ]);
      
      if (configSaved && prefsSaved) {
        this.showStatusMessage('Settings saved successfully!', 'success');
        
        // Update current settings
        this.currentConfig = config;
        this.currentPreferences = preferences;
        
        // Close window after a delay
        setTimeout(() => {
          window.close();
        }, 1500);
      } else {
        this.showStatusMessage('Failed to save settings', 'error');
      }
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showStatusMessage('Failed to save settings: ' + error.message, 'error');
    }
  }

  async exportData() {
    try {
      const data = await this.storageManager.exportUserData();
      
      // Create download
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-mentor-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
      
      this.showStatusMessage('Data exported successfully!', 'success');
    } catch (error) {
      console.error('Failed to export data:', error);
      this.showStatusMessage('Failed to export data: ' + error.message, 'error');
    }
  }

  async clearHistory() {
    if (confirm('Are you sure you want to clear all conversation history? This action cannot be undone.')) {
      try {
        const success = await this.storageManager.clearConversationHistory();
        if (success) {
          this.showStatusMessage('Conversation history cleared successfully!', 'success');
        } else {
          this.showStatusMessage('Failed to clear conversation history', 'error');
        }
      } catch (error) {
        console.error('Failed to clear history:', error);
        this.showStatusMessage('Failed to clear history: ' + error.message, 'error');
      }
    }
  }

  resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults? This will clear your API configuration.')) {
      // Reset form to defaults
      this.providerSelect.value = '';
      this.apiKeyInput.value = '';
      this.modelSelect.innerHTML = '<option value="">Select a model...</option>';
      this.customBaseUrl.value = '';
      this.customModel.value = '';
      this.temperatureInput.value = '0.7';
      this.temperatureValue.textContent = '0.7';
      this.showContextMenu.checked = true;
      this.autoSelectText.checked = true;
      this.themeSelect.value = 'light';
      
      // Hide custom fields
      this.customApiFields.style.display = 'none';
      
      // Hide test result
      this.testResult.style.display = 'none';
      
      // Validate form
      this.validateForm();
      
      this.showStatusMessage('Settings reset to defaults', 'success');
    }
  }

  showStatusMessage(message, type) {
    this.statusMessage.textContent = message;
    this.statusMessage.className = `status-message ${type}`;
    this.statusMessage.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      this.statusMessage.style.display = 'none';
    }, 3000);
  }
}

// Initialize settings manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SettingsManager();
});
