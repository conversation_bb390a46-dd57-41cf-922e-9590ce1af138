/**
 * Test Background Script Fix
 * Run this in the popup console to test if the background script is working
 */

async function testBackgroundFix() {
  console.log('🔧 Testing Background Script Fix...');
  
  try {
    // Test 1: Basic communication
    console.log('1. Testing basic background communication...');
    
    const startTime = Date.now();
    
    try {
      const response = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      const responseTime = Date.now() - startTime;
      
      console.log(`✅ Background responding in ${responseTime}ms:`, response);
      
      if (response && response.success !== undefined) {
        console.log('✅ Background script is working properly');
      } else {
        console.error('❌ Background script returned invalid response');
        return false;
      }
    } catch (error) {
      console.error('❌ Background script not responding:', error);
      return false;
    }
    
    // Test 2: API config retrieval
    console.log('\n2. Testing API config retrieval...');
    
    try {
      const configResponse = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
      console.log('Config response:', configResponse);
      
      if (configResponse && configResponse.success) {
        console.log('✅ Background can retrieve API config');
        
        if (configResponse.config) {
          console.log('Config details:', {
            provider: configResponse.config.provider,
            hasApiKey: !!configResponse.config.apiKey,
            model: configResponse.config.model
          });
        }
      } else {
        console.error('❌ Background cannot retrieve API config');
      }
    } catch (error) {
      console.error('❌ API config retrieval failed:', error);
    }
    
    // Test 3: AI request (the main test)
    console.log('\n3. Testing AI request...');
    
    const aiStartTime = Date.now();
    
    try {
      const aiResponse = await chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: 'Hello! Please respond with just "Test successful" to confirm the fix is working.',
        context: '',
        url: 'Test Page'
      });
      
      const aiResponseTime = Date.now() - aiStartTime;
      
      console.log(`AI request completed in ${aiResponseTime}ms:`, aiResponse);
      
      if (aiResponse && aiResponse.success) {
        console.log('✅ AI REQUEST SUCCESSFUL!');
        console.log('Response:', aiResponse.response);
        console.log('Provider:', aiResponse.provider);
        
        if (aiResponse.provider === 'groq' && aiResponseTime < 2000) {
          console.log('🚀 GroqCloud ultra-fast response confirmed!');
        }
        
        return true;
      } else {
        console.error('❌ AI request failed:', aiResponse?.error || 'Unknown error');
        return false;
      }
      
    } catch (error) {
      const aiResponseTime = Date.now() - aiStartTime;
      console.error(`❌ AI request error after ${aiResponseTime}ms:`, error);
      
      // Check if it's the old error
      if (error.message && error.message.includes('getAPIConfig')) {
        console.error('🚨 STILL GETTING THE OLD ERROR - Background script not fixed yet');
      }
      
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Function to test popup sending a message
async function testPopupMessage() {
  console.log('\n🧪 Testing Popup Message Sending...');
  
  if (window.popupManager && typeof window.popupManager.sendMessage === 'function') {
    const questionInput = document.getElementById('questionInput');
    
    if (questionInput) {
      const originalValue = questionInput.value;
      questionInput.value = 'Test message from popup - please respond briefly';
      
      try {
        console.log('Sending message via popup...');
        await window.popupManager.sendMessage();
        console.log('✅ Popup message sent successfully');
      } catch (error) {
        console.error('❌ Popup message failed:', error);
      } finally {
        questionInput.value = originalValue;
      }
    } else {
      console.error('❌ Question input not found');
    }
  } else {
    console.error('❌ PopupManager or sendMessage method not found');
  }
}

// Function to check background script console
function checkBackgroundConsole() {
  console.log('\n📋 Background Script Console Check:');
  console.log('1. Go to chrome://extensions/');
  console.log('2. Find "Universal AI Mentor"');
  console.log('3. Click "Inspect views: service worker"');
  console.log('4. Check console for these messages:');
  console.log('   - "✅ Dependencies loaded successfully"');
  console.log('   - "✅ LLM Client initialized"');
  console.log('   - "✅ Storage Manager initialized"');
  console.log('   - "✅ Background service fully ready"');
  console.log('');
  console.log('If you see errors like "LLMAPIClient is not defined", the fix needs more work.');
}

// Auto-run the test
testBackgroundFix().then(success => {
  console.log('\n📊 BACKGROUND SCRIPT TEST RESULT');
  console.log('==================================');
  
  if (success) {
    console.log('✅ BACKGROUND SCRIPT FIXED!');
    console.log('✅ No more "getAPIConfig" errors');
    console.log('✅ AI requests working properly');
    console.log('✅ Extension fully functional');
    
    // Test popup message sending too
    testPopupMessage();
  } else {
    console.log('❌ BACKGROUND SCRIPT STILL HAS ISSUES');
    console.log('❌ AI requests failing');
    console.log('❌ Need to check background script console');
    
    checkBackgroundConsole();
  }
});

// Export functions for manual use
window.testBackgroundFix = testBackgroundFix;
window.testPopupMessage = testPopupMessage;
window.checkBackgroundConsole = checkBackgroundConsole;
