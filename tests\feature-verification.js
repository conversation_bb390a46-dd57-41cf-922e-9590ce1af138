/**
 * Feature Verification Script
 * Validates that all claimed features actually exist and work
 */

class FeatureVerifier {
  constructor() {
    this.verificationResults = [];
    this.criticalIssues = [];
  }

  async verifyAllFeatures() {
    console.log('🔍 Starting Feature Verification...');
    
    // Verify each claimed feature
    await this.verifySidePanelStability();
    await this.verifyLoadingStateFix();
    await this.verifyMessagePersistence();
    await this.verifyChatHistoryInterface();
    await this.verifyTextSelectionFeature();
    await this.verifyGroqCloudIntegration();
    await this.verifyErrorHandling();
    
    return this.generateVerificationReport();
  }

  async verifySidePanelStability() {
    console.log('📋 Verifying Side Panel Stability...');
    
    const issues = [];
    
    try {
      // Check manifest for side panel permission
      const manifest = chrome.runtime.getManifest();
      if (!manifest.permissions.includes('sidePanel')) {
        issues.push('CRITICAL: sidePanel permission missing from manifest');
      }
      
      // Check for side panel configuration
      if (!manifest.side_panel || !manifest.side_panel.default_path) {
        issues.push('CRITICAL: side_panel configuration missing from manifest');
      }
      
      // Check for side panel toggle button in popup
      const popupHTML = await this.fetchFile('popup/popup.html');
      if (!popupHTML.includes('side-panel') && !popupHTML.includes('Open in Side Panel')) {
        issues.push('WARNING: Side panel toggle button not found in popup HTML');
      }
      
      // Check for side panel CSS
      const popupCSS = await this.fetchFile('popup/popup.css');
      if (!popupCSS.includes('side-panel-mode')) {
        issues.push('WARNING: Side panel CSS styles not found');
      }
      
      // Check for side panel JavaScript
      const popupJS = await this.fetchFile('popup/popup.js');
      if (!popupJS.includes('setupSidePanel') || !popupJS.includes('addSidePanelToggle')) {
        issues.push('CRITICAL: Side panel JavaScript methods missing');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify side panel features - ${error.message}`);
    }
    
    this.recordVerification('Side Panel Stability', issues);
  }

  async verifyLoadingStateFix() {
    console.log('⏳ Verifying Loading State Fix...');
    
    const issues = [];
    
    try {
      // Check popup script for loading state management
      const popupJS = await this.fetchFile('popup/popup.js');
      
      if (!popupJS.includes('this.isLoading')) {
        issues.push('CRITICAL: Loading state flag (this.isLoading) not found');
      }
      
      if (!popupJS.includes('Promise.race') || !popupJS.includes('timeout')) {
        issues.push('CRITICAL: Timeout handling with Promise.race not implemented');
      }
      
      if (!popupJS.includes('30000')) {
        issues.push('WARNING: 30-second timeout not found');
      }
      
      // Check background script for timeout handling
      const backgroundJS = await this.fetchFile('background/background.js');
      if (!backgroundJS.includes('timeoutPromise') && !backgroundJS.includes('setTimeout')) {
        issues.push('CRITICAL: Background script timeout handling missing');
      }
      
      // Check content script for auto-hide
      const contentJS = await this.fetchFile('content/content.js');
      if (!contentJS.includes('setTimeout') || !contentJS.includes('30000')) {
        issues.push('WARNING: Content script auto-hide timeout missing');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify loading state fix - ${error.message}`);
    }
    
    this.recordVerification('Loading State Fix', issues);
  }

  async verifyMessagePersistence() {
    console.log('💾 Verifying Message Persistence...');
    
    const issues = [];
    
    try {
      const popupJS = await this.fetchFile('popup/popup.js');
      
      // Check for session management methods
      if (!popupJS.includes('saveCurrentSession')) {
        issues.push('CRITICAL: saveCurrentSession method not found');
      }
      
      if (!popupJS.includes('restoreCurrentSession')) {
        issues.push('CRITICAL: restoreCurrentSession method not found');
      }
      
      if (!popupJS.includes('currentSessionMessages')) {
        issues.push('CRITICAL: currentSessionMessages array not found');
      }
      
      if (!popupJS.includes('clearCurrentSession')) {
        issues.push('WARNING: clearCurrentSession method not found');
      }
      
      // Check for storage usage
      if (!popupJS.includes('chrome.storage.local') || !popupJS.includes('currentSession')) {
        issues.push('CRITICAL: Session storage implementation missing');
      }
      
      // Check for clear chat button
      const popupHTML = await this.fetchFile('popup/popup.html');
      if (!popupHTML.includes('clearChatBtn') && !popupHTML.includes('Clear Chat')) {
        issues.push('WARNING: Clear chat button not found in HTML');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify message persistence - ${error.message}`);
    }
    
    this.recordVerification('Message Persistence', issues);
  }

  async verifyChatHistoryInterface() {
    console.log('📚 Verifying Chat History Interface...');
    
    const issues = [];
    
    try {
      const popupHTML = await this.fetchFile('popup/popup.html');
      const popupJS = await this.fetchFile('popup/popup.js');
      
      // Check for history screen elements
      if (!popupHTML.includes('historyScreen')) {
        issues.push('CRITICAL: History screen not found in HTML');
      }
      
      if (!popupHTML.includes('historyBtn')) {
        issues.push('CRITICAL: History button not found in HTML');
      }
      
      if (!popupHTML.includes('historyContent')) {
        issues.push('CRITICAL: History content area not found');
      }
      
      // Check for history management methods
      if (!popupJS.includes('loadHistoryContent')) {
        issues.push('CRITICAL: loadHistoryContent method missing');
      }
      
      if (!popupJS.includes('createHistoryItem')) {
        issues.push('WARNING: createHistoryItem method missing');
      }
      
      if (!popupJS.includes('showHistoryScreen')) {
        issues.push('CRITICAL: showHistoryScreen method missing');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify chat history interface - ${error.message}`);
    }
    
    this.recordVerification('Chat History Interface', issues);
  }

  async verifyTextSelectionFeature() {
    console.log('🎯 Verifying Text Selection Feature...');
    
    const issues = [];
    
    try {
      // Check manifest permissions
      const manifest = chrome.runtime.getManifest();
      if (!manifest.permissions.includes('contextMenus')) {
        issues.push('CRITICAL: contextMenus permission missing');
      }
      
      if (!manifest.permissions.includes('notifications')) {
        issues.push('WARNING: notifications permission missing (needed for fallback)');
      }
      
      // Check background script
      const backgroundJS = await this.fetchFile('background/background.js');
      if (!backgroundJS.includes('contextMenus.create')) {
        issues.push('CRITICAL: Context menu creation code missing');
      }
      
      if (!backgroundJS.includes('contextMenus.onClicked')) {
        issues.push('CRITICAL: Context menu click handler missing');
      }
      
      if (!backgroundJS.includes('showFallbackResponse') || !backgroundJS.includes('showFallbackError')) {
        issues.push('WARNING: Fallback notification methods missing');
      }
      
      // Check content script
      const contentJS = await this.fetchFile('content/content.js');
      if (!contentJS.includes('SHOW_LOADING') || !contentJS.includes('SHOW_RESPONSE')) {
        issues.push('CRITICAL: Content script message handling missing');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify text selection feature - ${error.message}`);
    }
    
    this.recordVerification('Text Selection Feature', issues);
  }

  async verifyGroqCloudIntegration() {
    console.log('⚡ Verifying GroqCloud Integration...');
    
    const issues = [];
    
    try {
      // Check LLM API client
      const llmJS = await this.fetchFile('lib/llm-api.js');
      
      if (!llmJS.includes('groq')) {
        issues.push('CRITICAL: GroqCloud provider not found in LLM client');
      }
      
      if (!llmJS.includes('api.groq.com')) {
        issues.push('CRITICAL: GroqCloud API endpoint not configured');
      }
      
      if (!llmJS.includes('llama-3.3-70b-versatile')) {
        issues.push('WARNING: GroqCloud models not properly defined');
      }
      
      if (!llmJS.includes('callGroq')) {
        issues.push('CRITICAL: callGroq method not implemented');
      }
      
      // Check settings integration
      const settingsHTML = await this.fetchFile('settings/settings.html');
      if (!settingsHTML.includes('GroqCloud') && !settingsHTML.includes('groq')) {
        issues.push('CRITICAL: GroqCloud option missing from settings');
      }
      
      // Check settings script
      const settingsJS = await this.fetchFile('settings/settings.js');
      if (!settingsJS.includes('testGroq') && !settingsJS.includes('groq')) {
        issues.push('WARNING: GroqCloud test method missing from settings');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify GroqCloud integration - ${error.message}`);
    }
    
    this.recordVerification('GroqCloud Integration', issues);
  }

  async verifyErrorHandling() {
    console.log('🚨 Verifying Error Handling...');
    
    const issues = [];
    
    try {
      const popupJS = await this.fetchFile('popup/popup.js');
      const backgroundJS = await this.fetchFile('background/background.js');
      
      // Check for try-catch blocks
      if (!popupJS.includes('try') || !popupJS.includes('catch')) {
        issues.push('WARNING: Error handling missing in popup script');
      }
      
      if (!backgroundJS.includes('try') || !backgroundJS.includes('catch')) {
        issues.push('WARNING: Error handling missing in background script');
      }
      
      // Check for error message display
      if (!popupJS.includes('addMessageToChat') || !popupJS.includes('error')) {
        issues.push('WARNING: Error message display functionality missing');
      }
      
      // Check for timeout error handling
      if (!popupJS.includes('timeout') || !popupJS.includes('Request timeout')) {
        issues.push('WARNING: Timeout error messages missing');
      }
      
    } catch (error) {
      issues.push(`ERROR: Failed to verify error handling - ${error.message}`);
    }
    
    this.recordVerification('Error Handling', issues);
  }

  async fetchFile(path) {
    try {
      const response = await fetch(chrome.runtime.getURL(path));
      if (!response.ok) {
        throw new Error(`Failed to fetch ${path}: ${response.status}`);
      }
      return await response.text();
    } catch (error) {
      throw new Error(`Cannot access file ${path}: ${error.message}`);
    }
  }

  recordVerification(featureName, issues) {
    const hasCritical = issues.some(issue => issue.includes('CRITICAL'));
    const hasWarnings = issues.some(issue => issue.includes('WARNING'));
    
    let status = 'VERIFIED';
    if (hasCritical) {
      status = 'FAILED';
      this.criticalIssues.push(...issues.filter(i => i.includes('CRITICAL')));
    } else if (hasWarnings) {
      status = 'PARTIAL';
    }
    
    this.verificationResults.push({
      feature: featureName,
      status: status,
      issues: issues,
      critical: hasCritical,
      warnings: hasWarnings
    });
    
    console.log(`${status === 'VERIFIED' ? '✅' : status === 'PARTIAL' ? '⚠️' : '❌'} ${featureName}: ${status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`  - ${issue}`));
    }
  }

  generateVerificationReport() {
    const totalFeatures = this.verificationResults.length;
    const verifiedFeatures = this.verificationResults.filter(r => r.status === 'VERIFIED').length;
    const failedFeatures = this.verificationResults.filter(r => r.status === 'FAILED').length;
    const partialFeatures = this.verificationResults.filter(r => r.status === 'PARTIAL').length;
    
    const report = {
      summary: {
        total: totalFeatures,
        verified: verifiedFeatures,
        failed: failedFeatures,
        partial: partialFeatures,
        successRate: (verifiedFeatures / totalFeatures) * 100
      },
      results: this.verificationResults,
      criticalIssues: this.criticalIssues,
      overallStatus: failedFeatures === 0 ? 'PASS' : 'FAIL'
    };
    
    console.log('\n📊 FEATURE VERIFICATION REPORT');
    console.log('================================');
    console.log(`Total Features: ${totalFeatures}`);
    console.log(`Verified: ${verifiedFeatures}`);
    console.log(`Failed: ${failedFeatures}`);
    console.log(`Partial: ${partialFeatures}`);
    console.log(`Success Rate: ${report.summary.successRate.toFixed(1)}%`);
    console.log(`Overall Status: ${report.overallStatus}`);
    
    if (this.criticalIssues.length > 0) {
      console.log('\n❌ CRITICAL ISSUES FOUND:');
      this.criticalIssues.forEach(issue => console.log(`- ${issue}`));
    }
    
    return report;
  }
}

// Export for use in test runner
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FeatureVerifier;
} else {
  window.FeatureVerifier = FeatureVerifier;
}
