/**
 * Comprehensive Extension Test Suite
 * Tests all claimed features and fixes
 */

class ExtensionTester {
  constructor() {
    this.testResults = [];
    this.failedTests = [];
    this.passedTests = [];
  }

  async runAllTests() {
    console.log('🧪 Starting Universal AI Mentor Extension Tests...');
    
    // Test 1: Side Panel Stability
    await this.testSidePanelStability();
    
    // Test 2: Loading State Bug Fix
    await this.testLoadingStateFix();
    
    // Test 3: Message Persistence
    await this.testMessagePersistence();
    
    // Test 4: Chat History Interface
    await this.testChatHistoryInterface();
    
    // Test 5: Text Selection Feature
    await this.testTextSelectionFeature();
    
    // Test 6: GroqCloud Integration
    await this.testGroqCloudIntegration();
    
    // Test 7: Error Handling
    await this.testErrorHandling();
    
    // Test 8: API Configuration
    await this.testAPIConfiguration();
    
    // Generate test report
    this.generateTestReport();
  }

  async testSidePanelStability() {
    console.log('📋 Testing Side Panel Stability...');
    
    try {
      // Test 1.1: Check manifest permissions
      const manifest = chrome.runtime.getManifest();
      this.assert(
        manifest.permissions.includes('sidePanel'),
        'Side panel permission exists in manifest'
      );
      
      // Test 1.2: Check side panel configuration
      this.assert(
        manifest.side_panel && manifest.side_panel.default_path,
        'Side panel configuration exists in manifest'
      );
      
      // Test 1.3: Check for side panel toggle button
      const sidePanelBtn = document.querySelector('#sidePanelBtn') || 
                          document.querySelector('[title="Open in Side Panel"]');
      this.assert(
        sidePanelBtn !== null,
        'Side panel toggle button exists in UI'
      );
      
      // Test 1.4: Check CSS for side panel mode
      const stylesheets = Array.from(document.styleSheets);
      let hasSidePanelCSS = false;
      stylesheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || []);
          hasSidePanelCSS = rules.some(rule => 
            rule.selectorText && rule.selectorText.includes('side-panel-mode')
          );
        } catch (e) {
          // Cross-origin stylesheet, skip
        }
      });
      this.assert(hasSidePanelCSS, 'Side panel CSS styles exist');
      
    } catch (error) {
      this.fail('Side Panel Stability', error.message);
    }
  }

  async testLoadingStateFix() {
    console.log('⏳ Testing Loading State Fix...');
    
    try {
      // Test 2.1: Check for loading state management
      const popupScript = await this.getScriptContent('popup/popup.js');
      this.assert(
        popupScript.includes('this.isLoading'),
        'Loading state flag exists in popup script'
      );
      
      // Test 2.2: Check for timeout handling
      this.assert(
        popupScript.includes('Promise.race') && popupScript.includes('timeout'),
        'Timeout handling implemented in popup'
      );
      
      // Test 2.3: Check background script timeout
      const backgroundScript = await this.getScriptContent('background/background.js');
      this.assert(
        backgroundScript.includes('timeoutPromise') || backgroundScript.includes('setTimeout'),
        'Timeout handling implemented in background script'
      );
      
      // Test 2.4: Check for loading spinner auto-hide
      const contentScript = await this.getScriptContent('content/content.js');
      this.assert(
        contentScript.includes('setTimeout') && contentScript.includes('30000'),
        'Auto-hide timeout implemented in content script'
      );
      
    } catch (error) {
      this.fail('Loading State Fix', error.message);
    }
  }

  async testMessagePersistence() {
    console.log('💾 Testing Message Persistence...');
    
    try {
      // Test 3.1: Check for session storage methods
      const popupScript = await this.getScriptContent('popup/popup.js');
      this.assert(
        popupScript.includes('saveCurrentSession'),
        'saveCurrentSession method exists'
      );
      
      this.assert(
        popupScript.includes('restoreCurrentSession'),
        'restoreCurrentSession method exists'
      );
      
      // Test 3.2: Check for currentSessionMessages array
      this.assert(
        popupScript.includes('currentSessionMessages'),
        'currentSessionMessages array exists'
      );
      
      // Test 3.3: Check for storage key usage
      this.assert(
        popupScript.includes('currentSession'),
        'currentSession storage key used'
      );
      
      // Test 3.4: Check for clear chat functionality
      const popupHTML = await this.getFileContent('popup/popup.html');
      this.assert(
        popupHTML.includes('clearChatBtn') || popupHTML.includes('Clear Chat'),
        'Clear chat button exists in HTML'
      );
      
      // Test 3.5: Simulate session save/restore
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const testSession = {
          messages: [
            { type: 'user', content: 'Test message', timestamp: Date.now() }
          ],
          timestamp: Date.now()
        };
        
        await chrome.storage.local.set({ currentSession: testSession });
        const result = await chrome.storage.local.get(['currentSession']);
        this.assert(
          result.currentSession && result.currentSession.messages.length === 1,
          'Session save/restore functionality works'
        );
      }
      
    } catch (error) {
      this.fail('Message Persistence', error.message);
    }
  }

  async testChatHistoryInterface() {
    console.log('📚 Testing Chat History Interface...');
    
    try {
      // Test 4.1: Check for history screen
      const popupHTML = await this.getFileContent('popup/popup.html');
      this.assert(
        popupHTML.includes('historyScreen'),
        'History screen exists in HTML'
      );
      
      // Test 4.2: Check for history button
      this.assert(
        popupHTML.includes('historyBtn'),
        'History button exists in HTML'
      );
      
      // Test 4.3: Check for history content area
      this.assert(
        popupHTML.includes('historyContent'),
        'History content area exists'
      );
      
      // Test 4.4: Check for history management methods
      const popupScript = await this.getScriptContent('popup/popup.js');
      this.assert(
        popupScript.includes('loadHistoryContent'),
        'loadHistoryContent method exists'
      );
      
      this.assert(
        popupScript.includes('createHistoryItem'),
        'createHistoryItem method exists'
      );
      
    } catch (error) {
      this.fail('Chat History Interface', error.message);
    }
  }

  async testTextSelectionFeature() {
    console.log('🎯 Testing Text Selection Feature...');
    
    try {
      // Test 5.1: Check manifest permissions
      const manifest = chrome.runtime.getManifest();
      this.assert(
        manifest.permissions.includes('contextMenus'),
        'Context menus permission exists'
      );
      
      // Test 5.2: Check background script context menu setup
      const backgroundScript = await this.getScriptContent('background/background.js');
      this.assert(
        backgroundScript.includes('contextMenus.create'),
        'Context menu creation code exists'
      );
      
      // Test 5.3: Check for context menu handler
      this.assert(
        backgroundScript.includes('contextMenus.onClicked'),
        'Context menu click handler exists'
      );
      
      // Test 5.4: Check for fallback notification methods
      this.assert(
        backgroundScript.includes('showFallbackResponse') || 
        backgroundScript.includes('notifications'),
        'Fallback notification methods exist'
      );
      
      // Test 5.5: Check content script message handling
      const contentScript = await this.getScriptContent('content/content.js');
      this.assert(
        contentScript.includes('SHOW_LOADING') && 
        contentScript.includes('SHOW_RESPONSE'),
        'Content script message handling exists'
      );
      
    } catch (error) {
      this.fail('Text Selection Feature', error.message);
    }
  }

  async testGroqCloudIntegration() {
    console.log('⚡ Testing GroqCloud Integration...');
    
    try {
      // Test 6.1: Check LLM API client for GroqCloud
      const llmScript = await this.getScriptContent('lib/llm-api.js');
      this.assert(
        llmScript.includes('groq'),
        'GroqCloud provider exists in LLM client'
      );
      
      // Test 6.2: Check for GroqCloud models
      this.assert(
        llmScript.includes('llama-3.3-70b-versatile'),
        'GroqCloud models are defined'
      );
      
      // Test 6.3: Check for GroqCloud API endpoint
      this.assert(
        llmScript.includes('api.groq.com'),
        'GroqCloud API endpoint is configured'
      );
      
      // Test 6.4: Check settings HTML for GroqCloud option
      const settingsHTML = await this.getFileContent('settings/settings.html');
      this.assert(
        settingsHTML.includes('GroqCloud') || settingsHTML.includes('groq'),
        'GroqCloud option exists in settings'
      );
      
      // Test 6.5: Check for callGroq method
      this.assert(
        llmScript.includes('callGroq'),
        'callGroq method exists in LLM client'
      );
      
    } catch (error) {
      this.fail('GroqCloud Integration', error.message);
    }
  }

  async testErrorHandling() {
    console.log('🚨 Testing Error Handling...');
    
    try {
      // Test 7.1: Check for timeout implementations
      const popupScript = await this.getScriptContent('popup/popup.js');
      this.assert(
        popupScript.includes('catch') && popupScript.includes('error'),
        'Error handling exists in popup script'
      );
      
      // Test 7.2: Check for error message display
      this.assert(
        popupScript.includes('addMessageToChat') && popupScript.includes('error'),
        'Error message display functionality exists'
      );
      
      // Test 7.3: Check background script error handling
      const backgroundScript = await this.getScriptContent('background/background.js');
      this.assert(
        backgroundScript.includes('try') && backgroundScript.includes('catch'),
        'Error handling exists in background script'
      );
      
      // Test 7.4: Check for notifications permission
      const manifest = chrome.runtime.getManifest();
      this.assert(
        manifest.permissions.includes('notifications'),
        'Notifications permission exists for fallback errors'
      );
      
    } catch (error) {
      this.fail('Error Handling', error.message);
    }
  }

  async testAPIConfiguration() {
    console.log('⚙️ Testing API Configuration...');
    
    try {
      // Test 8.1: Check settings page exists
      const settingsHTML = await this.getFileContent('settings/settings.html');
      this.assert(
        settingsHTML.includes('providerSelect'),
        'Provider selection exists in settings'
      );
      
      // Test 8.2: Check for API test functionality
      const settingsScript = await this.getScriptContent('settings/settings.js');
      this.assert(
        settingsScript.includes('testApiConnection'),
        'API test functionality exists'
      );
      
      // Test 8.3: Check for direct API test methods
      this.assert(
        settingsScript.includes('testGroq') || settingsScript.includes('testAPIDirectly'),
        'Direct API test methods exist'
      );
      
      // Test 8.4: Check storage manager
      const storageScript = await this.getScriptContent('lib/storage.js');
      this.assert(
        storageScript.includes('saveAPIConfig'),
        'API configuration storage exists'
      );
      
    } catch (error) {
      this.fail('API Configuration', error.message);
    }
  }

  // Helper methods
  async getFileContent(filePath) {
    try {
      const response = await fetch(chrome.runtime.getURL(filePath));
      return await response.text();
    } catch (error) {
      throw new Error(`Failed to load file: ${filePath}`);
    }
  }

  async getScriptContent(filePath) {
    return await this.getFileContent(filePath);
  }

  assert(condition, testName) {
    if (condition) {
      this.pass(testName);
    } else {
      this.fail(testName, 'Assertion failed');
    }
  }

  pass(testName) {
    this.passedTests.push(testName);
    this.testResults.push({ name: testName, status: 'PASS' });
    console.log(`✅ PASS: ${testName}`);
  }

  fail(testName, error) {
    this.failedTests.push({ name: testName, error });
    this.testResults.push({ name: testName, status: 'FAIL', error });
    console.log(`❌ FAIL: ${testName} - ${error}`);
  }

  generateTestReport() {
    console.log('\n📊 TEST REPORT');
    console.log('================');
    console.log(`Total Tests: ${this.testResults.length}`);
    console.log(`Passed: ${this.passedTests.length}`);
    console.log(`Failed: ${this.failedTests.length}`);
    console.log(`Success Rate: ${((this.passedTests.length / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (this.failedTests.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.failedTests.forEach(test => {
        console.log(`- ${test.name}: ${test.error}`);
      });
    }
    
    return {
      total: this.testResults.length,
      passed: this.passedTests.length,
      failed: this.failedTests.length,
      successRate: (this.passedTests.length / this.testResults.length) * 100,
      results: this.testResults
    };
  }
}

// Export for use in test runner
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExtensionTester;
} else {
  window.ExtensionTester = ExtensionTester;
}
