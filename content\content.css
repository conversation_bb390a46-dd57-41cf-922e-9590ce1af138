/**
 * Content Script Styles
 * Styles for AI response overlay and interactions
 */

.ai-mentor-response {
  position: absolute;
  z-index: 999999;
  max-width: 500px;
  min-width: 300px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.ai-mentor-response.ai-mentor-visible {
  opacity: 1;
  transform: translateY(0);
}

.ai-mentor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  font-weight: 600;
}

.ai-mentor-title {
  font-size: 16px;
  font-weight: 600;
}

.ai-mentor-actions {
  display: flex;
  gap: 8px;
}

.ai-mentor-close,
.ai-mentor-copy {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.ai-mentor-close:hover,
.ai-mentor-copy:hover {
  background: rgba(255, 255, 255, 0.3);
}

.ai-mentor-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.ai-mentor-context {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  border-left: 4px solid #667eea;
  font-size: 13px;
}

.ai-mentor-context strong {
  color: #333;
}

.ai-mentor-response-text {
  color: #333;
  line-height: 1.6;
}

.ai-mentor-response-text strong {
  font-weight: 600;
  color: #2c3e50;
}

.ai-mentor-response-text em {
  font-style: italic;
  color: #7f8c8d;
}

.ai-mentor-response-text code,
.inline-code {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e74c3c;
  border: 1px solid #e0e0e0;
}

/* AI Thinking Sections */
.ai-mentor-thinking-section {
  margin: 12px 0;
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  background: #f8f9fa;
}

.ai-mentor-thinking-toggle {
  width: 100%;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: none;
  padding: 8px 10px;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  font-weight: 500;
  color: #1565c0;
  transition: all 0.2s ease;
}

.ai-mentor-thinking-toggle:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
}

.ai-mentor-thinking-arrow {
  font-size: 11px;
  transition: transform 0.2s ease;
}

.ai-mentor-thinking-content {
  border-top: 1px solid #e3f2fd;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.ai-mentor-thinking-text {
  padding: 10px;
  font-size: 11px;
  line-height: 1.4;
  color: #666;
  font-style: italic;
  border-left: 3px solid #2196f3;
  margin: 6px;
  background: white;
  border-radius: 4px;
}

/* Code Block Styling */
.ai-mentor-code-block-container {
  margin: 12px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
}

.ai-mentor-code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: #2c3e50;
  color: white;
  font-size: 11px;
}

.ai-mentor-code-language {
  font-weight: 500;
  text-transform: uppercase;
  color: #3498db;
}

.ai-mentor-copy-code-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 3px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: background-color 0.2s ease;
}

.ai-mentor-copy-code-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.ai-mentor-code-block {
  margin: 0;
  padding: 10px;
  background: #2c3e50;
  color: #ecf0f1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
}

.ai-mentor-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  padding: 20px 0;
}

.ai-mentor-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: ai-mentor-spin 1s linear infinite;
}

@keyframes ai-mentor-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-mentor-error {
  color: #e74c3c;
  background: #fdf2f2;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 12px;
}

.ai-mentor-error-actions,
.ai-mentor-config-needed {
  text-align: center;
  padding: 12px 0;
}

.ai-mentor-config-needed h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.ai-mentor-config-needed p {
  margin: 0 0 16px 0;
  color: #666;
}

.ai-mentor-settings-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.ai-mentor-settings-btn:hover {
  background: #5a6fd8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ai-mentor-response {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
  }
  
  .ai-mentor-context {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .ai-mentor-response-text {
    color: #ecf0f1;
  }
  
  .ai-mentor-response-text strong {
    color: #3498db;
  }
  
  .ai-mentor-response-text code {
    background: #34495e;
    color: #e67e22;
  }
  
  .ai-mentor-error {
    background: #4a2c2a;
    color: #e74c3c;
  }
  
  .ai-mentor-config-needed h3 {
    color: #ecf0f1;
  }
  
  .ai-mentor-config-needed p {
    color: #bdc3c7;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .ai-mentor-response {
    max-width: calc(100vw - 20px);
    min-width: calc(100vw - 20px);
    left: 10px !important;
    right: 10px;
    transform: none !important;
  }
  
  .ai-mentor-content {
    max-height: 300px;
  }
}

/* Smooth scrollbar for content */
.ai-mentor-content::-webkit-scrollbar {
  width: 6px;
}

.ai-mentor-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-mentor-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ai-mentor-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for better UX */
.ai-mentor-response * {
  box-sizing: border-box;
}

/* Prevent text selection on UI elements */
.ai-mentor-header,
.ai-mentor-close,
.ai-mentor-copy,
.ai-mentor-settings-btn {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
