/**
 * Test Configuration Fix
 * Run this in the popup console to test if the "Not configured" issue is fixed
 */

async function testConfigFix() {
  console.log('🔧 Testing Configuration Fix...');
  
  try {
    // Step 1: Check what's actually in storage
    console.log('1. Checking storage contents...');
    
    const storageResult = await chrome.storage.sync.get(['apiConfig']);
    console.log('Storage result:', storageResult);
    
    if (storageResult.apiConfig) {
      console.log('✅ Configuration found in storage:', {
        provider: storageResult.apiConfig.provider,
        hasApiKey: !!storageResult.apiConfig.apiKey,
        apiKeyLength: storageResult.apiConfig.apiKey ? storageResult.apiConfig.apiKey.length : 0,
        model: storageResult.apiConfig.model
      });
      
      // Validate the configuration
      const isValid = !!(storageResult.apiConfig.apiKey && storageResult.apiConfig.provider);
      console.log(`Configuration is ${isValid ? 'VALID' : 'INVALID'}`);
      
      if (!isValid) {
        if (!storageResult.apiConfig.apiKey) {
          console.error('❌ Missing API key');
        }
        if (!storageResult.apiConfig.provider) {
          console.error('❌ Missing provider');
        }
      }
    } else {
      console.error('❌ No configuration found in storage');
      return false;
    }
    
    // Step 2: Test background script communication
    console.log('\n2. Testing background script...');
    
    try {
      const bgResponse = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      console.log('Background response:', bgResponse);
      
      if (bgResponse && bgResponse.success) {
        console.log(`Background script says configured: ${bgResponse.isConfigured}`);
      } else {
        console.warn('⚠️ Background script not responding properly');
      }
    } catch (error) {
      console.warn('⚠️ Background script error:', error);
    }
    
    // Step 3: Test popup's checkAPIStatus method
    console.log('\n3. Testing popup checkAPIStatus...');
    
    if (window.popupManager && typeof window.popupManager.checkAPIStatus === 'function') {
      try {
        await window.popupManager.checkAPIStatus();
        
        // Check the current screen
        const configScreen = document.getElementById('configScreen');
        const chatScreen = document.getElementById('chatScreen');
        
        if (configScreen.style.display === 'none' && chatScreen.style.display !== 'none') {
          console.log('✅ Popup correctly showing chat screen');
          return true;
        } else if (configScreen.style.display !== 'none') {
          console.error('❌ Popup still showing config screen');
          return false;
        } else {
          console.warn('⚠️ Unclear popup state');
          return false;
        }
      } catch (error) {
        console.error('❌ Popup checkAPIStatus failed:', error);
        return false;
      }
    } else {
      console.error('❌ PopupManager not found');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Function to manually force the popup to configured state
async function forceConfiguredState() {
  console.log('🔧 Forcing popup to configured state...');
  
  if (window.popupManager) {
    // Check if we have valid config in storage
    const storageResult = await chrome.storage.sync.get(['apiConfig']);
    
    if (storageResult.apiConfig && storageResult.apiConfig.apiKey && storageResult.apiConfig.provider) {
      console.log('✅ Valid config found, forcing UI update...');
      
      window.popupManager.updateStatusIndicator('connected', 'Connected');
      window.popupManager.showChatScreen();
      await window.popupManager.loadProviderInfo();
      
      console.log('✅ UI forced to configured state');
      return true;
    } else {
      console.error('❌ No valid configuration to force');
      return false;
    }
  } else {
    console.error('❌ PopupManager not found');
    return false;
  }
}

// Function to check current popup state
function checkPopupState() {
  console.log('📊 Current popup state:');
  
  const configScreen = document.getElementById('configScreen');
  const chatScreen = document.getElementById('chatScreen');
  const statusText = document.querySelector('.status-text');
  const statusDot = document.querySelector('.status-dot');
  
  console.log('Config screen visible:', configScreen.style.display !== 'none');
  console.log('Chat screen visible:', chatScreen.style.display !== 'none');
  console.log('Status text:', statusText ? statusText.textContent : 'Not found');
  console.log('Status dot class:', statusDot ? statusDot.className : 'Not found');
  
  if (window.popupManager) {
    console.log('PopupManager current screen:', window.popupManager.currentScreen);
  }
}

// Auto-run the test
testConfigFix().then(success => {
  console.log('\n📊 TEST RESULT');
  console.log('================');
  
  if (success) {
    console.log('✅ CONFIGURATION ISSUE FIXED!');
    console.log('✅ Popup correctly detects API configuration');
    console.log('✅ Chat screen is showing');
  } else {
    console.log('❌ CONFIGURATION ISSUE STILL EXISTS');
    console.log('❌ Popup not detecting API configuration properly');
    
    console.log('\n🔧 MANUAL FIX ATTEMPT');
    console.log('Trying to force configured state...');
    
    forceConfiguredState().then(forced => {
      if (forced) {
        console.log('✅ Manual fix successful - popup should now work');
      } else {
        console.log('❌ Manual fix failed - check your API configuration');
      }
    });
  }
});

// Export functions for manual use
window.testConfigFix = testConfigFix;
window.forceConfiguredState = forceConfiguredState;
window.checkPopupState = checkPopupState;
