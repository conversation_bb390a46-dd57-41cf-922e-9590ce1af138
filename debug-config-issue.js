/**
 * Debug Configuration Issue
 * Run this in the popup console to debug why "Not configured" is showing
 */

async function debugConfigIssue() {
  console.log('🔍 Debugging Configuration Issue...');
  
  try {
    // Step 1: Check if we can communicate with background script
    console.log('1. Testing background script communication...');
    
    try {
      const testResponse = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      console.log('✅ Background script responding:', testResponse);
      
      if (testResponse.success) {
        console.log(`API Configured: ${testResponse.isConfigured}`);
      } else {
        console.error('❌ Background script returned error');
      }
    } catch (error) {
      console.error('❌ Background script not responding:', error);
      return;
    }
    
    // Step 2: Check storage directly from popup
    console.log('\n2. Checking storage directly...');
    
    try {
      const storageResult = await chrome.storage.sync.get(['apiConfig']);
      console.log('Storage result:', storageResult);
      
      if (storageResult.apiConfig) {
        console.log('✅ API config found in storage:', {
          provider: storageResult.apiConfig.provider,
          hasApiKey: !!storageResult.apiConfig.apiKey,
          model: storageResult.apiConfig.model
        });
      } else {
        console.error('❌ No API config in storage');
      }
    } catch (error) {
      console.error('❌ Failed to read storage:', error);
    }
    
    // Step 3: Test GET_API_CONFIG message
    console.log('\n3. Testing GET_API_CONFIG message...');
    
    try {
      const configResponse = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
      console.log('Config response:', configResponse);
      
      if (configResponse.success && configResponse.config) {
        console.log('✅ Background script can read config:', {
          provider: configResponse.config.provider,
          hasApiKey: !!configResponse.config.apiKey,
          model: configResponse.config.model
        });
      } else {
        console.error('❌ Background script cannot read config');
      }
    } catch (error) {
      console.error('❌ GET_API_CONFIG failed:', error);
    }
    
    // Step 4: Check if StorageManager is working in background
    console.log('\n4. Testing background script initialization...');
    
    // Check background script console for errors
    console.log('Check the background script console (chrome://extensions -> inspect views: service worker)');
    console.log('Look for these messages:');
    console.log('- "✅ Dependencies loaded successfully"');
    console.log('- "✅ LLM Client initialized"');
    console.log('- "✅ Storage Manager initialized"');
    console.log('- "✅ Background service fully initialized"');
    
    // Step 5: Test popup's checkAPIStatus method directly
    console.log('\n5. Testing popup checkAPIStatus method...');
    
    if (window.popupManager && typeof window.popupManager.checkAPIStatus === 'function') {
      try {
        await window.popupManager.checkAPIStatus();
        console.log('✅ Popup checkAPIStatus completed');
      } catch (error) {
        console.error('❌ Popup checkAPIStatus failed:', error);
      }
    } else {
      console.error('❌ PopupManager or checkAPIStatus method not found');
    }
    
    // Step 6: Manual configuration check
    console.log('\n6. Manual configuration validation...');
    
    try {
      const manualCheck = await chrome.storage.sync.get(['apiConfig']);
      const config = manualCheck.apiConfig;
      
      if (config) {
        const isValid = !!(config.apiKey && config.provider);
        console.log(`Manual validation: ${isValid ? 'VALID' : 'INVALID'}`);
        console.log('Config details:', {
          hasApiKey: !!config.apiKey,
          apiKeyLength: config.apiKey ? config.apiKey.length : 0,
          provider: config.provider,
          model: config.model
        });
        
        if (!config.apiKey) {
          console.error('❌ Missing API key');
        }
        if (!config.provider) {
          console.error('❌ Missing provider');
        }
      } else {
        console.error('❌ No configuration found');
      }
    } catch (error) {
      console.error('❌ Manual check failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Function to force refresh the popup status
async function forceRefreshStatus() {
  console.log('🔄 Force refreshing popup status...');
  
  if (window.popupManager) {
    try {
      // Force re-check API status
      await window.popupManager.checkAPIStatus();
      console.log('✅ Status refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh status:', error);
    }
  } else {
    console.error('❌ PopupManager not found');
  }
}

// Function to manually set the popup to configured state (for testing)
function forceConfiguredState() {
  console.log('🔧 Forcing configured state for testing...');
  
  if (window.popupManager) {
    window.popupManager.updateStatusIndicator('connected', 'Connected');
    window.popupManager.showChatScreen();
    console.log('✅ Forced to configured state');
  } else {
    console.error('❌ PopupManager not found');
  }
}

// Auto-run debug
debugConfigIssue();

// Export functions for manual use
window.debugConfigIssue = debugConfigIssue;
window.forceRefreshStatus = forceRefreshStatus;
window.forceConfiguredState = forceConfiguredState;
