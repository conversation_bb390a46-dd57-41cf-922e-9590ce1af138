<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal AI Mentor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- Header -->
        <div class="popup-header">
            <div class="header-content">
                <h1 class="popup-title">AI Mentor</h1>
                <div class="header-actions">
                    <button id="clearChatBtn" class="icon-btn" title="Clear Chat">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </button>
                    <button id="settingsBtn" class="icon-btn" title="Settings">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                    </button>
                    <button id="historyBtn" class="icon-btn" title="History">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 3v5h5"></path>
                            <path d="M3.05 13A9 9 0 1 0 6 5.3L3 8"></path>
                            <path d="M12 7v5l4 2"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div id="statusIndicator" class="status-indicator">
                <span class="status-dot"></span>
                <span class="status-text">Checking...</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="popup-content">
            <!-- Configuration Required Screen -->
            <div id="configScreen" class="screen" style="display: none;">
                <div class="config-message">
                    <div class="config-icon">⚙️</div>
                    <h3>Configuration Required</h3>
                    <p>Please configure your API settings to start using AI Mentor.</p>
                    <button id="openSettingsBtn" class="primary-btn">Open Settings</button>
                </div>
            </div>

            <!-- Main Chat Screen -->
            <div id="chatScreen" class="screen">
                <!-- Selected Text Display -->
                <div id="selectedTextContainer" class="selected-text-container" style="display: none;">
                    <div class="selected-text-header">
                        <span class="selected-text-label">Selected text:</span>
                        <button id="clearSelectionBtn" class="clear-btn">×</button>
                    </div>
                    <div id="selectedTextContent" class="selected-text-content"></div>
                </div>

                <!-- Chat Messages -->
                <div id="chatMessages" class="chat-messages">
                    <div class="welcome-message">
                        <div class="welcome-icon">🤖</div>
                        <h3>Welcome to AI Mentor!</h3>
                        <p>Ask me anything or highlight text on any webpage for instant help.</p>
                        <div class="quick-actions">
                            <button class="quick-action-btn" data-prompt="Explain this concept in simple terms">
                                💡 Explain Simply
                            </button>
                            <button class="quick-action-btn" data-prompt="What are the key points here?">
                                📝 Key Points
                            </button>
                            <button class="quick-action-btn" data-prompt="Can you give me an example?">
                                🔍 Give Example
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-container">
                        <textarea 
                            id="questionInput" 
                            placeholder="Ask me anything..."
                            rows="1"
                        ></textarea>
                        <button id="sendBtn" class="send-btn" disabled>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9"></polygon>
                            </svg>
                        </button>
                    </div>
                    <div class="input-footer">
                        <span class="provider-info" id="providerInfo"></span>
                        <span class="shortcut-hint">Ctrl+Enter to send</span>
                    </div>
                </div>
            </div>

            <!-- History Screen -->
            <div id="historyScreen" class="screen" style="display: none;">
                <div class="screen-header">
                    <button id="backFromHistoryBtn" class="back-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 12H5"></path>
                            <path d="M12 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <h2>Conversation History</h2>
                    <button id="clearHistoryBtn" class="clear-history-btn">Clear All</button>
                </div>
                <div id="historyContent" class="history-content">
                    <div class="loading-spinner">Loading history...</div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <span>Getting AI response...</span>
            </div>
        </div>
    </div>

    <script src="../lib/llm-api.js"></script>
    <script src="../lib/storage.js"></script>
    <script src="popup.js"></script>
</body>
</html>
