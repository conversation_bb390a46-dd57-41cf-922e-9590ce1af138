/**
 * Popup Script
 * Main extension popup functionality
 */

class PopupManager {
  constructor() {
    this.storageManager = new StorageManager();
    this.currentScreen = 'chat';
    this.selectedText = '';
    this.conversationHistory = [];
    this.currentSessionMessages = [];
    this.isLoading = false;

    this.init();
  }

  async init() {
    // Initialize DOM elements
    this.initializeElements();

    // Apply theme
    await this.applyTheme();

    // Set up event listeners
    this.setupEventListeners();

    // Check API configuration and update UI
    await this.checkAPIStatus();

    // Get selected text from current tab
    await this.getSelectedTextFromTab();

    // Load conversation history
    await this.loadConversationHistory();

    // Restore current session messages
    await this.restoreCurrentSession();

    // Set up side panel if supported
    this.setupSidePanel();
  }

  initializeElements() {
    // Screens
    this.configScreen = document.getElementById('configScreen');
    this.chatScreen = document.getElementById('chatScreen');
    this.historyScreen = document.getElementById('historyScreen');
    
    // Header elements
    this.statusIndicator = document.getElementById('statusIndicator');
    this.statusDot = this.statusIndicator.querySelector('.status-dot');
    this.statusText = this.statusIndicator.querySelector('.status-text');
    
    // Chat elements
    this.selectedTextContainer = document.getElementById('selectedTextContainer');
    this.selectedTextContent = document.getElementById('selectedTextContent');
    this.chatMessages = document.getElementById('chatMessages');
    this.questionInput = document.getElementById('questionInput');
    this.sendBtn = document.getElementById('sendBtn');
    this.providerInfo = document.getElementById('providerInfo');
    this.scrollToBottomBtn = document.getElementById('scrollToBottomBtn');
    
    // History elements
    this.historyContent = document.getElementById('historyContent');
    
    // Loading overlay
    this.loadingOverlay = document.getElementById('loadingOverlay');
  }

  setupEventListeners() {
    // Header buttons
    document.getElementById('sidePanelBtn').addEventListener('click', async () => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.sidePanel.open({ tabId: tab.id });
        window.close();
      } catch (error) {
        console.error('Failed to open side panel:', error);
      }
    });

    document.getElementById('clearChatBtn').addEventListener('click', () => {
      if (confirm('Clear current chat session?')) {
        this.clearCurrentSession();
      }
    });

    document.getElementById('settingsBtn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });

    document.getElementById('historyBtn').addEventListener('click', () => {
      this.showHistoryScreen();
    });

    // Refresh status button
    document.getElementById('refreshStatusBtn').addEventListener('click', async () => {
      console.log('🔄 Manual refresh triggered');
      await this.checkAPIStatus();
    });

    // Config screen
    document.getElementById('openSettingsBtn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });
    
    // Selected text
    document.getElementById('clearSelectionBtn').addEventListener('click', () => {
      this.clearSelectedText();
    });
    
    // Chat input
    this.questionInput.addEventListener('input', () => {
      this.updateSendButton();
      this.autoResizeTextarea();
    });
    
    this.questionInput.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        this.sendMessage();
      }
    });
    
    this.sendBtn.addEventListener('click', () => {
      this.sendMessage();
    });
    
    // Quick action buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('quick-action-btn')) {
        const prompt = e.target.getAttribute('data-prompt');
        this.questionInput.value = prompt;
        this.updateSendButton();
        this.questionInput.focus();
      }
    });
    
    // Scroll to bottom button
    this.scrollToBottomBtn.addEventListener('click', () => {
      this.scrollToBottom();
    });

    // Chat messages scroll detection
    this.chatMessages.addEventListener('scroll', () => {
      this.handleChatScroll();
    });

    // History screen
    document.getElementById('backFromHistoryBtn').addEventListener('click', () => {
      this.showChatScreen();
    });

    document.getElementById('clearHistoryBtn').addEventListener('click', () => {
      this.clearHistory();
    });
  }

  async checkAPIStatus() {
    console.log('🔍 Checking API status...');

    try {
      // First try to get status from background script
      const response = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      console.log('Background script response:', response);

      if (response && response.success && response.isConfigured) {
        console.log('✅ API configured via background script');
        this.updateStatusIndicator('connected', 'Connected');
        this.showChatScreen();
        await this.loadProviderInfo();
        return;
      }

      // Fallback: Check storage directly if background script says not configured
      console.log('🔄 Checking storage directly as fallback...');
      const storageResult = await chrome.storage.sync.get(['apiConfig']);
      console.log('Direct storage result:', storageResult);

      if (storageResult.apiConfig && storageResult.apiConfig.apiKey && storageResult.apiConfig.provider) {
        console.log('✅ API configured via direct storage check');
        this.updateStatusIndicator('connected', 'Connected');
        this.showChatScreen();
        await this.loadProviderInfo();
      } else {
        console.log('❌ API not configured');
        this.updateStatusIndicator('error', 'Not configured');
        this.showConfigScreen();
      }

    } catch (error) {
      console.error('❌ Failed to check API status:', error);

      // Final fallback: Check storage directly
      try {
        console.log('🔄 Final fallback: checking storage directly...');
        const storageResult = await chrome.storage.sync.get(['apiConfig']);

        if (storageResult.apiConfig && storageResult.apiConfig.apiKey && storageResult.apiConfig.provider) {
          console.log('✅ API configured via final fallback');
          this.updateStatusIndicator('connected', 'Connected');
          this.showChatScreen();
          await this.loadProviderInfo();
        } else {
          console.log('❌ API not configured (final check)');
          this.updateStatusIndicator('error', 'Error');
          this.showConfigScreen();
        }
      } catch (storageError) {
        console.error('❌ Storage check failed:', storageError);
        this.updateStatusIndicator('error', 'Error');
        this.showConfigScreen();
      }
    }
  }

  async loadProviderInfo() {
    try {
      // Try to get config from background script first
      const response = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
      if (response && response.success && response.config) {
        const providerName = response.config.provider.charAt(0).toUpperCase() +
                           response.config.provider.slice(1);
        const modelName = response.config.model || 'Default model';
        this.providerInfo.textContent = `${providerName} • ${modelName}`;
        return;
      }

      // Fallback: Get config directly from storage
      const storageResult = await chrome.storage.sync.get(['apiConfig']);
      if (storageResult.apiConfig) {
        const config = storageResult.apiConfig;
        const providerName = config.provider.charAt(0).toUpperCase() +
                           config.provider.slice(1);
        const modelName = config.model || 'Default model';
        this.providerInfo.textContent = `${providerName} • ${modelName}`;
      }
    } catch (error) {
      console.error('Failed to load provider info:', error);
    }
  }

  updateStatusIndicator(status, text) {
    this.statusDot.className = `status-dot ${status}`;
    this.statusText.textContent = text;
  }

  showConfigScreen() {
    this.configScreen.style.display = 'flex';
    this.chatScreen.style.display = 'none';
    this.historyScreen.style.display = 'none';
    this.currentScreen = 'config';
  }

  showChatScreen() {
    this.configScreen.style.display = 'none';
    this.chatScreen.style.display = 'flex';
    this.historyScreen.style.display = 'none';
    this.currentScreen = 'chat';
  }

  showHistoryScreen() {
    this.configScreen.style.display = 'none';
    this.chatScreen.style.display = 'none';
    this.historyScreen.style.display = 'flex';
    this.currentScreen = 'history';
    this.loadHistoryContent();
  }

  async getSelectedTextFromTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const response = await chrome.tabs.sendMessage(tab.id, { type: 'GET_PAGE_CONTEXT' });
      
      if (response && response.selectedText) {
        this.setSelectedText(response.selectedText);
      }
    } catch (error) {
      // Content script might not be loaded, ignore error
      console.log('Could not get selected text from tab');
    }
  }

  setSelectedText(text) {
    if (text && text.trim()) {
      this.selectedText = text.trim();
      this.selectedTextContent.textContent = this.selectedText;
      this.selectedTextContainer.style.display = 'block';
      
      // Hide welcome message if it exists
      const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
      if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
      }
    }
  }

  clearSelectedText() {
    this.selectedText = '';
    this.selectedTextContainer.style.display = 'none';
    
    // Show welcome message if no conversation history
    if (this.chatMessages.children.length === 1) {
      const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
      if (welcomeMessage) {
        welcomeMessage.style.display = 'block';
      }
    }
  }

  updateSendButton() {
    const hasText = this.questionInput.value.trim().length > 0;
    this.sendBtn.disabled = !hasText;
  }

  autoResizeTextarea() {
    this.questionInput.style.height = 'auto';
    this.questionInput.style.height = Math.min(this.questionInput.scrollHeight, 100) + 'px';
  }

  async sendMessage() {
    const question = this.questionInput.value.trim();
    if (!question || this.isLoading) return;

    // Add user message to chat
    this.addMessageToChat('user', question);

    // Clear input
    this.questionInput.value = '';
    this.updateSendButton();
    this.autoResizeTextarea();

    // Show typing animation in chat
    this.showTypingAnimation();
    this.isLoading = true;

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Start simulated thinking process
      this.simulateThinkingProcess(question);

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 30000)
      );

      const responsePromise = chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: question,
        context: this.selectedText,
        url: tab?.url || 'Unknown',
        conversationHistory: this.currentSessionMessages
      });

      const response = await Promise.race([responsePromise, timeoutPromise]);

      if (response && response.success) {
        // Show main typing indicator while processing final response
        this.showMainTypingIndicator();

        // Small delay to show the transition
        await new Promise(resolve => setTimeout(resolve, 500));

        this.addMessageToChat('ai', response.response, response.provider);

        // Clear selected text after successful response
        if (this.selectedText) {
          this.clearSelectedText();
        }
      } else {
        this.addMessageToChat('error', response?.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      this.addMessageToChat('error', `Failed to get AI response: ${error.message}`);
    } finally {
      this.hideTypingAnimation();
      this.isLoading = false;
    }
  }

  addMessageToChat(type, content, provider = null) {
    // Hide welcome message
    const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.style.display = 'none';
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    messageDiv.setAttribute('data-timestamp', Date.now());
    messageDiv.setAttribute('data-type', type);

    if (type === 'user') {
      messageDiv.innerHTML = `<div class="message-user">${this.escapeHtml(content)}</div>`;
    } else if (type === 'ai') {
      const providerBadge = provider ? `<div class="provider-badge">${provider}</div>` : '';
      messageDiv.innerHTML = `
        <div class="message-ai">
          ${providerBadge}
          ${this.formatAIResponse(content)}
        </div>
      `;
    } else if (type === 'error') {
      messageDiv.innerHTML = `
        <div class="message-ai" style="border-color: #ff4757; background: #fff5f5;">
          <strong>Error:</strong> ${this.escapeHtml(content)}
        </div>
      `;
    }

    this.chatMessages.appendChild(messageDiv);

    // Ensure smooth scrolling to bottom
    this.scrollToBottom();

    // Save message to current session
    this.currentSessionMessages.push({
      type,
      content,
      provider,
      timestamp: Date.now()
    });

    // Save current session to storage
    this.saveCurrentSession();
  }

  formatAIResponse(response) {
    console.log('🎨 Formatting AI response...');
    console.log('Original response length:', response.length);

    let formattedResponse = response;

    // First, extract thinking sections and replace with placeholders
    const thinkingSections = [];
    formattedResponse = formattedResponse.replace(/<think>([\s\S]*?)<\/think>/gi, (_, thinkContent) => {
      const placeholder = `__THINKING_${thinkingSections.length}__`;
      thinkingSections.push(thinkContent.trim());
      console.log(`📝 Found thinking section ${thinkingSections.length}:`, thinkContent.substring(0, 50) + '...');
      return placeholder;
    });

    console.log('Thinking sections found:', thinkingSections.length);

    // If we have thinking sections, they were already shown in real-time, so we'll create collapsed toggles

    // Escape HTML for the main content
    formattedResponse = this.escapeHtml(formattedResponse);

    // Format code blocks with copy buttons
    formattedResponse = this.formatCodeBlocks(formattedResponse);

    // Format inline code
    formattedResponse = formattedResponse.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

    // Format bold text
    formattedResponse = formattedResponse.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Format italic text
    formattedResponse = formattedResponse.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Format headers
    formattedResponse = formattedResponse.replace(/^### (.*$)/gm, '<h3 class="response-header">$1</h3>');
    formattedResponse = formattedResponse.replace(/^## (.*$)/gm, '<h2 class="response-header">$1</h2>');
    formattedResponse = formattedResponse.replace(/^# (.*$)/gm, '<h1 class="response-header">$1</h1>');

    // Format lists
    formattedResponse = formattedResponse.replace(/^- (.*$)/gm, '<li class="response-list-item">$1</li>');
    formattedResponse = formattedResponse.replace(/(<li class="response-list-item">.*<\/li>)/s, '<ul class="response-list">$1</ul>');

    // Format line breaks
    formattedResponse = formattedResponse.replace(/\n/g, '<br>');

    // Now replace thinking placeholders with formatted thinking sections
    thinkingSections.forEach((thinkContent, index) => {
      const thinkId = 'think-' + Date.now() + '-' + index;
      const thinkingHtml = `
        <div class="thinking-section">
          <button class="thinking-toggle" onclick="window.popupManager.toggleThinking('${thinkId}')" title="Show/hide AI reasoning">
            🧠 AI Thinking Process (Already shown in real-time)
            <span class="thinking-arrow">▼</span>
          </button>
          <div class="thinking-content" id="${thinkId}" style="display: none;">
            <div class="thinking-text">${this.escapeHtml(thinkContent)}</div>
          </div>
        </div>
      `;
      formattedResponse = formattedResponse.replace(`__THINKING_${index}__`, thinkingHtml);
      console.log(`🧠 Replaced thinking placeholder ${index} with ID: ${thinkId}`);
    });

    console.log('✅ Formatting complete. Final length:', formattedResponse.length);
    console.log('Final response preview:', formattedResponse.substring(0, 200) + '...');

    return formattedResponse;
  }



  toggleThinking(thinkId) {
    console.log('🧠 Toggling thinking section:', thinkId);

    const thinkElement = document.getElementById(thinkId);
    if (!thinkElement) {
      console.error('❌ Thinking element not found:', thinkId);
      return;
    }

    const toggleBtn = thinkElement.previousElementSibling;
    const arrow = toggleBtn ? toggleBtn.querySelector('.thinking-arrow') : null;

    const isHidden = thinkElement.style.display === 'none' ||
                    getComputedStyle(thinkElement).display === 'none' ||
                    !thinkElement.style.display;

    if (isHidden) {
      thinkElement.style.display = 'block';
      thinkElement.style.opacity = '0';
      thinkElement.style.maxHeight = '0';
      thinkElement.style.overflow = 'hidden';
      thinkElement.style.transition = 'all 0.3s ease';

      // Trigger animation
      setTimeout(() => {
        thinkElement.style.opacity = '1';
        thinkElement.style.maxHeight = '500px';
      }, 10);

      if (arrow) arrow.textContent = '▲';
      if (toggleBtn) toggleBtn.classList.add('thinking-expanded');
      console.log('✅ Thinking section expanded');
    } else {
      thinkElement.style.opacity = '0';
      thinkElement.style.maxHeight = '0';

      setTimeout(() => {
        thinkElement.style.display = 'none';
      }, 300);

      if (arrow) arrow.textContent = '▼';
      if (toggleBtn) toggleBtn.classList.remove('thinking-expanded');
      console.log('✅ Thinking section collapsed');
    }
  }

  formatCodeBlocks(text) {
    // Match code blocks with language specification
    const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g;

    return text.replace(codeBlockRegex, (_, language, code) => {
      const lang = language || 'text';
      const codeId = 'code-' + Math.random().toString(36).substring(2, 11);

      return `
        <div class="code-block-container">
          <div class="code-block-header">
            <span class="code-language">${lang}</span>
            <button class="copy-code-btn" onclick="window.popupManager.copyCode('${codeId}')" title="Copy code">
              📋 Copy
            </button>
          </div>
          <pre class="code-block" id="${codeId}"><code class="language-${lang}">${code.trim()}</code></pre>
        </div>
      `;
    });
  }

  copyCode(codeId) {
    console.log('📋 Copying code from element:', codeId);

    const codeElement = document.getElementById(codeId);
    if (!codeElement) {
      console.error('❌ Code element not found:', codeId);
      return;
    }

    const code = codeElement.textContent || codeElement.innerText;
    console.log('📝 Code to copy:', code.substring(0, 50) + '...');

    // Find the copy button
    const copyBtn = codeElement.closest('.code-block-container')?.querySelector('.copy-code-btn');

    if (!copyBtn) {
      console.error('❌ Copy button not found');
      return;
    }

    // Use modern clipboard API with fallback
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(code).then(() => {
        this.showCopyFeedback(copyBtn, true);
        console.log('✅ Code copied to clipboard');
      }).catch(err => {
        console.error('❌ Clipboard API failed:', err);
        this.fallbackCopyCode(code, copyBtn);
      });
    } else {
      this.fallbackCopyCode(code, copyBtn);
    }
  }

  fallbackCopyCode(code, copyBtn) {
    try {
      // Create temporary textarea for fallback copy
      const textarea = document.createElement('textarea');
      textarea.value = code;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.pointerEvents = 'none';
      document.body.appendChild(textarea);

      textarea.select();
      textarea.setSelectionRange(0, 99999);

      const successful = document.execCommand('copy');
      document.body.removeChild(textarea);

      this.showCopyFeedback(copyBtn, successful);

      if (successful) {
        console.log('✅ Code copied using fallback method');
      } else {
        console.error('❌ Fallback copy failed');
      }
    } catch (err) {
      console.error('❌ Copy operation failed:', err);
      this.showCopyFeedback(copyBtn, false);
    }
  }

  showCopyFeedback(copyBtn, success) {
    const originalText = copyBtn.textContent;
    const originalBackground = copyBtn.style.background;

    if (success) {
      copyBtn.textContent = '✅ Copied!';
      copyBtn.style.background = '#28a745';
      copyBtn.style.color = 'white';
    } else {
      copyBtn.textContent = '❌ Failed';
      copyBtn.style.background = '#dc3545';
      copyBtn.style.color = 'white';
    }

    // Add pulse animation
    copyBtn.style.transform = 'scale(0.95)';
    copyBtn.style.transition = 'all 0.1s ease';

    setTimeout(() => {
      copyBtn.style.transform = 'scale(1)';
    }, 100);

    // Reset after 2 seconds
    setTimeout(() => {
      copyBtn.textContent = originalText;
      copyBtn.style.background = originalBackground;
      copyBtn.style.color = '';
      copyBtn.style.transform = '';
    }, 2000);
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  showLoading(show) {
    this.loadingOverlay.style.display = show ? 'flex' : 'none';
  }

  showTypingAnimation() {
    // Remove any existing typing animation
    this.hideTypingAnimation();

    const typingMessage = document.createElement('div');
    typingMessage.className = 'message ai-message';
    typingMessage.id = 'typing-animation';
    typingMessage.innerHTML = `
      <div class="message-ai">
        <div class="thinking-section" id="realtime-thinking">
          <button class="thinking-toggle thinking-expanded" id="realtime-thinking-toggle" type="button" onclick="toggleRealtimeThinking()">
            🧠 AI Thinking Process
            <span class="thinking-arrow">▲</span>
          </button>
          <div class="thinking-content" id="realtime-thinking-content" style="display: block; opacity: 1; max-height: 200px;">
            <div class="thinking-text" id="realtime-thinking-text">Starting to think...</div>
          </div>
        </div>
        <div class="typing-indicator" id="main-typing-indicator" style="display: none;">
          <div class="typing-text">Generating response</div>
          <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
        </div>
      </div>
    `;

    this.chatMessages.appendChild(typingMessage);
    this.scrollToBottom();

    console.log('✅ Real-time thinking animation created with onclick handler');
  }

  hideTypingAnimation() {
    const typingAnimation = document.getElementById('typing-animation');
    if (typingAnimation) {
      typingAnimation.remove();
    }
  }

  toggleRealtimeThinking() {
    console.log('🔄 Toggling real-time thinking...');

    const thinkingContent = document.getElementById('realtime-thinking-content');
    const toggle = document.getElementById('realtime-thinking-toggle');
    const arrow = toggle?.querySelector('.thinking-arrow');

    if (!thinkingContent || !toggle) {
      console.error('❌ Real-time thinking elements not found');
      return;
    }

    // Check current visibility using computed style
    const computedStyle = window.getComputedStyle(thinkingContent);
    const isVisible = computedStyle.display !== 'none' && thinkingContent.style.display !== 'none';

    console.log('Current visibility:', isVisible);

    if (isVisible) {
      // Hide thinking content
      thinkingContent.style.display = 'none';
      thinkingContent.style.opacity = '0';
      thinkingContent.style.maxHeight = '0';
      toggle.classList.remove('thinking-expanded');
      if (arrow) arrow.textContent = '▼';
      console.log('✅ Thinking collapsed');
    } else {
      // Show thinking content
      thinkingContent.style.display = 'block';
      thinkingContent.style.opacity = '1';
      thinkingContent.style.maxHeight = '200px';
      toggle.classList.add('thinking-expanded');
      if (arrow) arrow.textContent = '▲';
      console.log('✅ Thinking expanded');
    }

    this.scrollToBottom();
  }

  updateRealtimeThinking(text) {
    const thinkingText = document.getElementById('realtime-thinking-text');
    if (thinkingText) {
      thinkingText.textContent = text;
      this.scrollToBottom();
    }
  }

  showMainTypingIndicator() {
    const thinkingSection = document.getElementById('realtime-thinking');
    const typingIndicator = document.getElementById('main-typing-indicator');

    if (thinkingSection && typingIndicator) {
      // Collapse thinking section
      const thinkingContent = document.getElementById('realtime-thinking-content');
      const toggle = document.getElementById('realtime-thinking-toggle');
      const arrow = toggle?.querySelector('.thinking-arrow');

      if (thinkingContent) {
        thinkingContent.style.display = 'none';
        if (toggle) toggle.classList.remove('thinking-expanded');
        if (arrow) arrow.textContent = '▼';
      }

      // Show main typing indicator
      typingIndicator.style.display = 'flex';
      this.scrollToBottom();
    }
  }

  async simulateThinkingProcess(question) {
    const thinkingSteps = this.generateThinkingSteps(question);

    for (let i = 0; i < thinkingSteps.length; i++) {
      const step = thinkingSteps[i];
      this.updateRealtimeThinking(step);

      // Random delay between 800-2000ms to simulate real thinking
      const delay = Math.random() * 1200 + 800;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Final thinking step
    this.updateRealtimeThinking("Formulating comprehensive response...");
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  generateThinkingSteps(question) {
    const generalSteps = [
      "Analyzing the question...",
      "Understanding the context...",
      "Considering different approaches...",
      "Evaluating relevant information...",
      "Structuring the response...",
      "Checking for accuracy...",
      "Preparing final answer..."
    ];

    // Customize steps based on question type
    if (question.toLowerCase().includes('code') || question.toLowerCase().includes('program') || question.toLowerCase().includes('function')) {
      const codeSteps = [
        "Analyzing the coding request...",
        "Considering programming best practices...",
        "Thinking about the most efficient approach...",
        "Planning the code structure...",
        "Considering edge cases and error handling...",
        "Preparing code examples and explanations...",
        "Reviewing syntax and optimization..."
      ];
      return this.shuffleAndSelectSteps(codeSteps, 4, 6);
    } else if (question.toLowerCase().includes('explain') || question.toLowerCase().includes('what is') || question.toLowerCase().includes('define')) {
      const explainSteps = [
        "Breaking down the concept...",
        "Identifying key components...",
        "Considering different perspectives...",
        "Organizing information logically...",
        "Preparing clear explanations...",
        "Adding relevant examples...",
        "Ensuring comprehensive coverage..."
      ];
      return this.shuffleAndSelectSteps(explainSteps, 4, 6);
    } else if (question.toLowerCase().includes('how to') || question.toLowerCase().includes('help') || question.toLowerCase().includes('guide')) {
      const howToSteps = [
        "Understanding the problem...",
        "Identifying possible solutions...",
        "Evaluating different approaches...",
        "Considering step-by-step instructions...",
        "Thinking about potential challenges...",
        "Preparing actionable guidance...",
        "Organizing steps logically..."
      ];
      return this.shuffleAndSelectSteps(howToSteps, 4, 6);
    }

    return this.shuffleAndSelectSteps(generalSteps, 4, 6);
  }

  shuffleAndSelectSteps(steps, min, max) {
    // Shuffle array and select random number of steps
    const shuffled = [...steps].sort(() => Math.random() - 0.5);
    const count = Math.floor(Math.random() * (max - min + 1)) + min;
    return shuffled.slice(0, count);
  }

  async applyTheme() {
    try {
      const result = await chrome.storage.sync.get(['userPreferences']);
      const preferences = result.userPreferences || {};
      const theme = preferences.theme || 'light';

      // Remove existing theme classes
      document.body.classList.remove('light-mode', 'dark-mode');

      if (theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        document.body.classList.add(prefersDark ? 'dark-mode' : 'light-mode');
      } else {
        document.body.classList.add(`${theme}-mode`);
      }

      console.log('✅ Theme applied:', theme);
    } catch (error) {
      console.error('Failed to apply theme:', error);
      // Default to light mode
      document.body.classList.add('light-mode');
    }
  }

  async loadConversationHistory() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_CONVERSATION_HISTORY' });
      if (response.success) {
        this.conversationHistory = response.history;
      }
    } catch (error) {
      console.error('Failed to load conversation history:', error);
    }
  }

  async loadHistoryContent() {
    this.historyContent.innerHTML = '<div class="loading-spinner">Loading history...</div>';
    
    try {
      await this.loadConversationHistory();
      
      if (this.conversationHistory.length === 0) {
        this.historyContent.innerHTML = `
          <div style="text-align: center; padding: 32px; color: #666;">
            <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
            <h3>No conversations yet</h3>
            <p>Your conversation history will appear here.</p>
          </div>
        `;
        return;
      }

      this.historyContent.innerHTML = '';
      
      this.conversationHistory.forEach(item => {
        const historyItem = this.createHistoryItem(item);
        this.historyContent.appendChild(historyItem);
      });
    } catch (error) {
      console.error('Failed to load history content:', error);
      this.historyContent.innerHTML = `
        <div style="text-align: center; padding: 32px; color: #ff4757;">
          <h3>Error loading history</h3>
          <p>Please try again later.</p>
        </div>
      `;
    }
  }

  createHistoryItem(item) {
    const div = document.createElement('div');
    div.className = 'history-item';
    
    const date = new Date(item.timestamp).toLocaleDateString();
    const time = new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    
    div.innerHTML = `
      <div class="history-item-header">
        <span class="history-item-date">${date} ${time}</span>
        <span class="history-item-provider">${item.provider}</span>
      </div>
      <div class="history-item-question">${this.escapeHtml(item.question)}</div>
      ${item.context ? `<div class="history-item-context">Context: "${this.escapeHtml(item.context)}"</div>` : ''}
    `;
    
    div.addEventListener('click', () => {
      // Switch to chat screen and populate with this conversation
      this.showChatScreen();
      this.questionInput.value = item.question;
      if (item.context) {
        this.setSelectedText(item.context);
      }
      this.updateSendButton();
    });
    
    return div;
  }

  async clearHistory() {
    if (confirm('Are you sure you want to clear all conversation history?')) {
      try {
        const response = await chrome.runtime.sendMessage({ type: 'CLEAR_HISTORY' });
        if (response.success) {
          this.conversationHistory = [];
          this.loadHistoryContent();
        }
      } catch (error) {
        console.error('Failed to clear history:', error);
      }
    }
  }

  // New methods for session management and side panel support

  async saveCurrentSession() {
    try {
      await chrome.storage.local.set({
        'currentSession': {
          messages: this.currentSessionMessages,
          selectedText: this.selectedText,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('Failed to save current session:', error);
    }
  }

  async restoreCurrentSession() {
    try {
      const result = await chrome.storage.local.get(['currentSession']);
      const session = result.currentSession;

      if (session && session.messages && session.messages.length > 0) {
        // Check if session is recent (within last hour)
        const oneHour = 60 * 60 * 1000;
        if (Date.now() - session.timestamp < oneHour) {
          this.currentSessionMessages = session.messages;

          // Restore selected text
          if (session.selectedText) {
            this.setSelectedText(session.selectedText);
          }

          // Restore messages to UI
          session.messages.forEach(msg => {
            this.addMessageToChatUI(msg.type, msg.content, msg.provider);
          });
        } else {
          // Clear old session
          await chrome.storage.local.remove(['currentSession']);
        }
      }
    } catch (error) {
      console.error('Failed to restore current session:', error);
    }
  }

  scrollToBottom() {
    // Multiple methods to ensure scrolling works
    setTimeout(() => {
      // Method 1: Direct scroll
      this.chatMessages.scrollTop = this.chatMessages.scrollHeight;

      // Method 2: Smooth scroll behavior
      this.chatMessages.scrollTo({
        top: this.chatMessages.scrollHeight,
        behavior: 'smooth'
      });

      // Method 3: Scroll last message into view
      const lastMessage = this.chatMessages.lastElementChild;
      if (lastMessage && !lastMessage.classList.contains('scroll-to-bottom-btn')) {
        lastMessage.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });
      }
    }, 100); // Small delay to ensure DOM is updated
  }

  handleChatScroll() {
    // Show/hide scroll to bottom button based on scroll position
    const { scrollTop, scrollHeight, clientHeight } = this.chatMessages;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50; // 50px threshold

    if (isNearBottom) {
      this.scrollToBottomBtn.style.display = 'none';
    } else {
      // Only show if there are messages (not just welcome message)
      const hasMessages = this.chatMessages.children.length > 1;
      if (hasMessages) {
        this.scrollToBottomBtn.style.display = 'flex';
      }
    }
  }

  addMessageToChatUI(type, content, provider = null) {
    // Similar to addMessageToChat but without saving to session (to avoid duplicates)
    const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.style.display = 'none';
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    messageDiv.setAttribute('data-timestamp', Date.now());
    messageDiv.setAttribute('data-type', type);

    if (type === 'user') {
      messageDiv.innerHTML = `<div class="message-user">${this.escapeHtml(content)}</div>`;
    } else if (type === 'ai') {
      const providerBadge = provider ? `<div class="provider-badge">${provider}</div>` : '';
      messageDiv.innerHTML = `
        <div class="message-ai">
          ${providerBadge}
          ${this.formatAIResponse(content)}
        </div>
      `;
    } else if (type === 'error') {
      messageDiv.innerHTML = `
        <div class="message-ai" style="border-color: #ff4757; background: #fff5f5;">
          <strong>Error:</strong> ${this.escapeHtml(content)}
        </div>
      `;
    }

    this.chatMessages.appendChild(messageDiv);

    // Ensure smooth scrolling to bottom
    this.scrollToBottom();
  }

  setupSidePanel() {
    // Function to check and apply side panel mode
    const checkSidePanelMode = () => {
      const isSidePanel = window.location.search.includes('sidePanel=true') ||
                         window.outerWidth > 450 || // Wider than popup suggests side panel
                         window.innerWidth > 450;

      if (isSidePanel) {
        document.body.classList.add('side-panel-mode');
        console.log('🔧 Side panel mode activated');

        // Force full width layout
        document.body.style.width = '100vw';
        document.body.style.minWidth = '100vw';
        document.body.style.maxWidth = 'none';

        const container = document.querySelector('.popup-container');
        if (container) {
          container.style.width = '100vw';
          container.style.minWidth = '100vw';
          container.style.maxWidth = 'none';
        }
      } else {
        document.body.classList.remove('side-panel-mode');
      }
    };

    // Initial check
    checkSidePanelMode();

    // Listen for window resize to handle dynamic width changes
    window.addEventListener('resize', () => {
      checkSidePanelMode();
    });

    // Also check after a short delay to ensure proper detection
    setTimeout(checkSidePanelMode, 100);
  }

  clearCurrentSession() {
    this.currentSessionMessages = [];
    chrome.storage.local.remove(['currentSession']);

    // Clear chat messages from UI
    const messages = this.chatMessages.querySelectorAll('.message');
    messages.forEach(msg => msg.remove());

    // Show welcome message again
    const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.style.display = 'block';
    }
  }
}

// Initialize popup manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.popupManager = new PopupManager();
});

// Global function for real-time thinking toggle (accessible from HTML)
window.toggleRealtimeThinking = function() {
  console.log('🌍 Global toggleRealtimeThinking called');
  if (window.popupManager) {
    window.popupManager.toggleRealtimeThinking();
  } else {
    console.error('❌ PopupManager not initialized');

    // Fallback direct toggle
    const thinkingContent = document.getElementById('realtime-thinking-content');
    const toggle = document.getElementById('realtime-thinking-toggle');
    const arrow = toggle?.querySelector('.thinking-arrow');

    if (thinkingContent && toggle) {
      console.log('🔄 Using fallback toggle method');
      const isVisible = thinkingContent.style.display !== 'none';

      if (isVisible) {
        thinkingContent.style.display = 'none';
        toggle.classList.remove('thinking-expanded');
        if (arrow) arrow.textContent = '▼';
      } else {
        thinkingContent.style.display = 'block';
        toggle.classList.add('thinking-expanded');
        if (arrow) arrow.textContent = '▲';
      }
    }
  }
};
