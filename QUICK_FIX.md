# Quick Fix for Extension Loading

## Issue
The extension fails to load because the icon files are missing.

## Immediate Solution
I've temporarily removed the icon references from the manifest.json so the extension can load without icons.

## To Add Icons Later

### Option 1: Use the Icon Generator (Recommended)
1. Open `icons/convert-to-png.html` in your browser
2. Click "Generate All PNG Icons"
3. The browser will download 4 PNG files: icon16.png, icon32.png, icon48.png, icon128.png
4. Move these files to the `icons/` directory
5. Add the icon references back to manifest.json:

```json
"action": {
  "default_popup": "popup/popup.html",
  "default_title": "Universal AI Mentor",
  "default_icon": {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  }
},

"icons": {
  "16": "icons/icon16.png",
  "32": "icons/icon32.png",
  "48": "icons/icon48.png",
  "128": "icons/icon128.png"
},
```

### Option 2: Create Simple Icons Manually
1. Use any image editor (Paint, GIMP, Photoshop, etc.)
2. Create 4 square images with these exact sizes: 16x16, 32x32, 48x48, 128x128 pixels
3. Use a simple design with the brand colors (#667eea, #764ba2)
4. Save as PNG files with the names: icon16.png, icon32.png, icon48.png, icon128.png
5. Place them in the `icons/` directory
6. Add the icon references back to manifest.json (see code above)

### Option 3: Use Online Icon Generators
1. Visit [Favicon.io](https://favicon.io/favicon-generator/) or [RealFaviconGenerator](https://realfavicongenerator.net/)
2. Create an icon with text "AI" or upload a simple design
3. Download the generated icons
4. Rename them to match the required names
5. Place in `icons/` directory and update manifest.json

## Current Status
✅ Extension should now load successfully without icons
✅ All functionality will work normally
⚠️ Extension will use default browser icons until you add custom ones

## Test the Extension
1. Load the extension in Chrome/Edge (`chrome://extensions/`)
2. Enable Developer mode
3. Click "Load unpacked" and select the Extension directory
4. The extension should load successfully now
5. Click the extension icon to test the popup
6. Try right-clicking on selected text to test context menu

The extension is fully functional without custom icons - they're just for branding and visual appeal!
