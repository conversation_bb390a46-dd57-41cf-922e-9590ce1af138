/**
 * Debug API Issue
 * Run this in the popup console to debug the timeout issue
 */

async function debugAPIIssue() {
  console.log('🔍 Debugging API Issue...');
  
  try {
    // Step 1: Check if API config exists
    console.log('1. Checking API configuration...');
    const configResponse = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
    console.log('Config response:', configResponse);
    
    if (!configResponse.success || !configResponse.config) {
      console.error('❌ No API configuration found!');
      return;
    }
    
    const config = configResponse.config;
    console.log('✅ API Config found:', {
      provider: config.provider,
      hasApiKey: !!config.apiKey,
      model: config.model,
      customBaseUrl: config.customBaseUrl,
      customModel: config.customModel
    });
    
    // Step 2: Test direct API call (like settings page does)
    console.log('\n2. Testing direct API call...');
    
    if (config.provider === 'groq') {
      try {
        const directResponse = await fetch('https://api.groq.com/openai/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`
          },
          body: JSON.stringify({
            model: config.model || 'llama-3.3-70b-versatile',
            messages: [
              { role: 'system', content: 'You are a helpful assistant.' },
              { role: 'user', content: 'Hello! This is a test message. Please respond with "Test successful!"' }
            ],
            max_tokens: 50
          })
        });
        
        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('✅ Direct API call successful:', data.choices[0].message.content);
        } else {
          const error = await directResponse.json();
          console.error('❌ Direct API call failed:', error);
        }
      } catch (error) {
        console.error('❌ Direct API call error:', error);
      }
    }
    
    // Step 3: Test background script message
    console.log('\n3. Testing background script message...');
    
    const startTime = Date.now();
    
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: 'Hello! This is a test message.',
        context: '',
        url: 'Debug Test'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`Response received in ${duration}ms:`, response);
      
      if (response.success) {
        console.log('✅ Background script working correctly!');
      } else {
        console.error('❌ Background script error:', response.error);
      }
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`❌ Background script timeout after ${duration}ms:`, error);
    }
    
    // Step 4: Check if background script is responding at all
    console.log('\n4. Testing basic background script communication...');
    
    try {
      const statusResponse = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      console.log('✅ Background script responding:', statusResponse);
    } catch (error) {
      console.error('❌ Background script not responding:', error);
    }
    
    // Step 5: Check LLM client directly
    console.log('\n5. Testing LLM client directly...');
    
    try {
      // Load LLM client script
      const llmResponse = await fetch(chrome.runtime.getURL('lib/llm-api.js'));
      const llmScript = await llmResponse.text();
      
      // Check if GroqCloud is properly implemented
      if (llmScript.includes('callGroq') && llmScript.includes('api.groq.com')) {
        console.log('✅ LLM client has GroqCloud implementation');
        
        // Try to create LLM client instance
        const llmClient = new LLMAPIClient();
        console.log('✅ LLM client created successfully');
        
        // Check supported providers
        const providers = llmClient.getSupportedProviders();
        console.log('Supported providers:', providers);
        
        // Check if GroqCloud provider exists
        const groqProvider = llmClient.supportedProviders.groq;
        if (groqProvider) {
          console.log('✅ GroqCloud provider found:', groqProvider);
        } else {
          console.error('❌ GroqCloud provider not found');
        }
        
      } else {
        console.error('❌ LLM client missing GroqCloud implementation');
      }
    } catch (error) {
      console.error('❌ LLM client test failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Auto-run debug
debugAPIIssue();

// Export for manual use
window.debugAPIIssue = debugAPIIssue;
