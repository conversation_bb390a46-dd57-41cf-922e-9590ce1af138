/**
 * Test Timeout Fix
 * Run this in the popup console to test if the timeout issue is fixed
 */

async function testTimeoutFix() {
  console.log('🔧 Testing Timeout Fix...');
  
  try {
    // Test 1: Check if background script is responding
    console.log('1. Testing background script communication...');
    
    const startTime = Date.now();
    
    try {
      const statusResponse = await chrome.runtime.sendMessage({ 
        type: 'CHECK_API_STATUS' 
      });
      
      const responseTime = Date.now() - startTime;
      console.log(`✅ Background script responding in ${responseTime}ms:`, statusResponse);
      
    } catch (error) {
      console.error('❌ Background script not responding:', error);
      return false;
    }
    
    // Test 2: Check API configuration
    console.log('\n2. Testing API configuration...');
    
    try {
      const configResponse = await chrome.runtime.sendMessage({ 
        type: 'GET_API_CONFIG' 
      });
      
      if (configResponse.success && configResponse.config) {
        console.log('✅ API config loaded:', {
          provider: configResponse.config.provider,
          hasApiKey: !!configResponse.config.apiKey,
          model: configResponse.config.model
        });
      } else {
        console.error('❌ No API configuration found');
        return false;
      }
      
    } catch (error) {
      console.error('❌ Failed to get API config:', error);
      return false;
    }
    
    // Test 3: Test actual AI request
    console.log('\n3. Testing AI request...');
    
    const aiStartTime = Date.now();
    
    try {
      const aiResponse = await chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: 'Hello! This is a test message. Please respond briefly.',
        context: '',
        url: 'Test Page'
      });
      
      const aiResponseTime = Date.now() - aiStartTime;
      
      if (aiResponse.success) {
        console.log(`✅ AI request successful in ${aiResponseTime}ms!`);
        console.log('Response:', aiResponse.response.substring(0, 100) + '...');
        console.log('Provider:', aiResponse.provider);
        
        if (aiResponse.provider === 'groq' && aiResponseTime < 2000) {
          console.log('🚀 GroqCloud ultra-fast response confirmed!');
        }
        
        return true;
      } else {
        console.error('❌ AI request failed:', aiResponse.error);
        return false;
      }
      
    } catch (error) {
      const aiResponseTime = Date.now() - aiStartTime;
      console.error(`❌ AI request timeout after ${aiResponseTime}ms:`, error);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Test the popup's sendMessage method directly
async function testPopupSendMessage() {
  console.log('\n🧪 Testing Popup SendMessage Method...');
  
  if (window.popupManager && typeof window.popupManager.sendMessage === 'function') {
    console.log('✅ PopupManager found, testing sendMessage...');
    
    // Temporarily override the input value
    const questionInput = document.getElementById('questionInput');
    if (questionInput) {
      const originalValue = questionInput.value;
      questionInput.value = 'Test message from popup';
      
      try {
        // Call the popup's sendMessage method
        await window.popupManager.sendMessage();
        console.log('✅ Popup sendMessage completed successfully');
      } catch (error) {
        console.error('❌ Popup sendMessage failed:', error);
      } finally {
        // Restore original value
        questionInput.value = originalValue;
      }
    } else {
      console.error('❌ Question input not found');
    }
  } else {
    console.error('❌ PopupManager or sendMessage method not found');
  }
}

// Run both tests
async function runAllTests() {
  console.log('🧪 Running All Timeout Fix Tests...\n');
  
  const backgroundTest = await testTimeoutFix();
  await testPopupSendMessage();
  
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  
  if (backgroundTest) {
    console.log('✅ TIMEOUT ISSUE FIXED!');
    console.log('✅ Background script communication working');
    console.log('✅ AI requests completing successfully');
    console.log('✅ No more infinite loading spinners');
  } else {
    console.log('❌ TIMEOUT ISSUE STILL EXISTS');
    console.log('❌ Background script or AI requests failing');
    console.log('❌ Further debugging needed');
  }
  
  return backgroundTest;
}

// Auto-run the tests
runAllTests().then(success => {
  if (success) {
    console.log('\n🎉 EXTENSION READY TO USE!');
  } else {
    console.log('\n🚨 EXTENSION NEEDS MORE FIXES!');
  }
});

// Export for manual use
window.testTimeoutFix = testTimeoutFix;
window.testPopupSendMessage = testPopupSendMessage;
window.runAllTests = runAllTests;
