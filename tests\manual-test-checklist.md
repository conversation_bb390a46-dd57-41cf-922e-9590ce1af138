# Manual Test Checklist for Universal AI Mentor Extension

## 🧪 Pre-Test Setup

### Requirements
- [ ] Chrome/Edge browser (version 114+ for side panel)
- [ ] Extension loaded in developer mode
- [ ] Valid API key for at least one provider (GroqCloud recommended)
- [ ] Test webpage with selectable text

### Initial Verification
- [ ] Extension icon appears in browser toolbar
- [ ] Extension loads without console errors
- [ ] Settings page opens correctly
- [ ] API configuration can be saved

---

## 📋 Test 1: Side Panel Stability

### Test 1.1: Side Panel Toggle Button
- [ ] Open extension popup
- [ ] Verify side panel toggle button exists in header (should be leftmost icon)
- [ ] <PERSON><PERSON> has tooltip "Open in Side Panel"
- [ ] <PERSON><PERSON> shows side panel icon (rectangle with vertical line)

### Test 1.2: Side Panel Functionality
- [ ] Click side panel toggle button
- [ ] Extension opens in browser side panel (Chrome 114+)
- [ ] Side panel stays open when clicking on webpage
- [ ] All functionality works in side panel mode
- [ ] U<PERSON> adjusts properly for side panel width

### Test 1.3: Popup Stability
- [ ] Open regular popup (click extension icon)
- [ ] Send a message to AI
- [ ] Click elsewhere on webpage
- [ ] **CRITICAL**: Popup should remain stable and not close unexpectedly

**Expected Result**: ✅ Side panel provides persistent access, popup is stable

---

## ⏳ Test 2: Loading State Bug Fix

### Test 2.1: Normal API Response
- [ ] Configure GroqCloud API in settings
- [ ] Open extension popup
- [ ] Send message: "Hello, test message"
- [ ] Observe loading spinner appears
- [ ] **CRITICAL**: Spinner resolves to actual response (not infinite)
- [ ] Response appears within 0.1-0.5 seconds (GroqCloud speed)

### Test 2.2: Timeout Handling
- [ ] Configure invalid API key
- [ ] Send a message
- [ ] Observe loading spinner
- [ ] **CRITICAL**: Should show error message within 30 seconds, not infinite loading
- [ ] Error message should be descriptive

### Test 2.3: Network Issues
- [ ] Disconnect internet
- [ ] Send a message
- [ ] **CRITICAL**: Should timeout gracefully with network error message
- [ ] Reconnect internet and verify normal operation resumes

**Expected Result**: ✅ No infinite loading, proper timeout handling, clear error messages

---

## 💾 Test 3: Message Persistence

### Test 3.1: Session Persistence
- [ ] Open extension popup
- [ ] Send 3-4 messages to AI and receive responses
- [ ] Note the conversation content
- [ ] Close popup completely
- [ ] Reopen extension popup
- [ ] **CRITICAL**: All previous messages should be restored exactly
- [ ] Conversation continues from where it left off

### Test 3.2: Clear Chat Functionality
- [ ] With messages in current session
- [ ] Click "Clear Chat" button (trash icon in header)
- [ ] Confirm the clear action
- [ ] **CRITICAL**: All messages should be cleared
- [ ] Welcome message should reappear
- [ ] New conversation should start fresh

### Test 3.3: Session Expiration
- [ ] Send some messages
- [ ] Wait 1+ hour (or manually set old timestamp in storage)
- [ ] Reopen popup
- [ ] **CRITICAL**: Old session should be cleared automatically
- [ ] Fresh session should start

**Expected Result**: ✅ Messages persist between sessions, clear chat works, auto-expiration functions

---

## 📚 Test 4: Chat History Interface

### Test 4.1: History Screen Access
- [ ] Open extension popup
- [ ] Click history button (clock icon in header)
- [ ] **CRITICAL**: History screen should open
- [ ] Should show "Conversation History" title
- [ ] Back button should be present

### Test 4.2: History Display
- [ ] Have some saved conversations (send messages first)
- [ ] Open history screen
- [ ] **CRITICAL**: Previous conversations should be listed
- [ ] Each item should show date, time, provider
- [ ] Question preview should be visible
- [ ] Context (selected text) should be shown if applicable

### Test 4.3: History Interaction
- [ ] Click on a history item
- [ ] **CRITICAL**: Should return to chat screen
- [ ] Question should be populated in input field
- [ ] Context should be restored if applicable

### Test 4.4: Clear History
- [ ] In history screen, click "Clear All" button
- [ ] Confirm the action
- [ ] **CRITICAL**: All history should be cleared
- [ ] Should show "No conversations yet" message

**Expected Result**: ✅ History interface works, displays conversations, allows interaction

---

## 🎯 Test 5: Text Selection Feature

### Test 5.1: Context Menu Appearance
- [ ] Go to any webpage (use test-page.html)
- [ ] Highlight some text
- [ ] Right-click on selected text
- [ ] **CRITICAL**: "Ask AI Mentor about [text]" option should appear
- [ ] Option should show preview of selected text

### Test 5.2: Context Menu Functionality
- [ ] Select text: "Machine learning is a subset of artificial intelligence"
- [ ] Right-click → "Ask AI Mentor about [text]"
- [ ] **CRITICAL**: Loading overlay should appear on page
- [ ] Should show selected text in overlay
- [ ] Should receive AI response in overlay (not infinite "checking...")

### Test 5.3: Response Display
- [ ] After context menu selection
- [ ] **CRITICAL**: Response should appear in page overlay
- [ ] Overlay should be positioned near selected text
- [ ] Should show provider badge (e.g., "groq")
- [ ] Copy button should work
- [ ] Close button should work

### Test 5.4: Fallback Handling
- [ ] Test on page where content script might fail
- [ ] Use context menu on selected text
- [ ] **CRITICAL**: If overlay fails, should show system notification
- [ ] Notification should contain response preview

**Expected Result**: ✅ Text selection works reliably, shows responses, has fallback handling

---

## ⚡ Test 6: GroqCloud Integration

### Test 6.1: Provider Configuration
- [ ] Open extension settings
- [ ] Provider dropdown should include "GroqCloud (Llama, Gemma, Ultra-Fast)"
- [ ] Select GroqCloud provider
- [ ] Model dropdown should populate with GroqCloud models:
  - [ ] llama-3.3-70b-versatile
  - [ ] llama-3.1-8b-instant
  - [ ] gemma2-9b-it
  - [ ] deepseek-r1-distill-llama-70b
  - [ ] qwen-qwq-32b
  - [ ] mistral-saba-24b

### Test 6.2: API Connection Test
- [ ] Enter valid GroqCloud API key
- [ ] Select "llama-3.3-70b-versatile" model
- [ ] Click "Test API Connection"
- [ ] **CRITICAL**: Should show success message quickly (1-2 seconds)
- [ ] Response preview should be shown

### Test 6.3: Speed Performance
- [ ] Configure GroqCloud with llama-3.3-70b-versatile
- [ ] Send message: "Explain quantum computing in simple terms"
- [ ] **CRITICAL**: Response should arrive in 0.1-0.5 seconds
- [ ] Compare with other providers (should be noticeably faster)

### Test 6.4: Model Switching
- [ ] Test different GroqCloud models
- [ ] llama-3.1-8b-instant should be fastest
- [ ] All models should work correctly
- [ ] Provider badge should show "groq"

**Expected Result**: ✅ GroqCloud integration works, ultra-fast responses, all models available

---

## 🚨 Test 7: Error Handling

### Test 7.1: Invalid API Key
- [ ] Configure invalid API key
- [ ] Send a message
- [ ] **CRITICAL**: Should show clear error message
- [ ] Should not show infinite loading
- [ ] Error should mention authentication/API key issue

### Test 7.2: Network Errors
- [ ] Disconnect internet
- [ ] Send a message
- [ ] **CRITICAL**: Should show network error message
- [ ] Should timeout within 30 seconds

### Test 7.3: Rate Limiting
- [ ] If possible, trigger rate limiting
- [ ] **CRITICAL**: Should show rate limit error message
- [ ] Should suggest waiting or upgrading plan

### Test 7.4: Invalid Model
- [ ] Manually set invalid model name in storage
- [ ] Send a message
- [ ] **CRITICAL**: Should show model not found error
- [ ] Should suggest checking configuration

**Expected Result**: ✅ All errors handled gracefully with clear messages

---

## ⚙️ Test 8: Settings and Configuration

### Test 8.1: Settings Page
- [ ] Click settings button in popup
- [ ] Settings page should open in new tab
- [ ] All providers should be listed
- [ ] Form validation should work

### Test 8.2: API Test Functionality
- [ ] For each provider (OpenAI, GroqCloud, Anthropic, Google):
- [ ] Enter valid API key
- [ ] Click "Test API Connection"
- [ ] **CRITICAL**: Should show success/failure clearly
- [ ] Should not show infinite spinning

### Test 8.3: Settings Persistence
- [ ] Configure API settings
- [ ] Save settings
- [ ] Close and reopen extension
- [ ] **CRITICAL**: Settings should be preserved
- [ ] API should work immediately

**Expected Result**: ✅ Settings work correctly, API tests function, persistence works

---

## 📊 Test Results Summary

### Critical Issues Status
- [ ] ✅ Side Panel Stability: FIXED
- [ ] ✅ Loading State Bug: FIXED  
- [ ] ✅ Message Persistence: IMPLEMENTED
- [ ] ✅ Chat History Interface: FUNCTIONAL
- [ ] ✅ Text Selection Feature: WORKING
- [ ] ✅ GroqCloud Integration: ULTRA-FAST
- [ ] ✅ Error Handling: ROBUST

### Performance Benchmarks
- [ ] GroqCloud response time: < 0.5 seconds
- [ ] Other providers: 1-3 seconds
- [ ] Timeout handling: 30 seconds max
- [ ] Session restore: < 1 second

### User Experience
- [ ] No infinite loading spinners
- [ ] Clear error messages
- [ ] Persistent conversations
- [ ] Stable popup/side panel
- [ ] Fast, responsive interface

---

## 🔧 Debugging Commands

If tests fail, use these console commands:

```javascript
// Check current session
chrome.storage.local.get(['currentSession']).then(console.log);

// Check API config
chrome.storage.sync.get(['apiConfig']).then(console.log);

// Clear session manually
chrome.storage.local.remove(['currentSession']);

// Test storage
chrome.storage.local.set({test: 'value'}).then(() => 
  chrome.storage.local.get(['test']).then(console.log)
);
```

---

## ✅ Pass Criteria

**ALL TESTS MUST PASS** for the extension to be considered fully functional:

1. **No infinite loading states**
2. **Messages persist between sessions**  
3. **Side panel/popup stability**
4. **Text selection works reliably**
5. **GroqCloud ultra-fast responses**
6. **Clear error handling**
7. **All UI elements functional**
8. **Settings save and restore**

**If any test fails, the corresponding feature needs to be fixed before claiming it works.**
