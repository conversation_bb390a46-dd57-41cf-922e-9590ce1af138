/**
 * Popup Styles
 * Main extension popup interface styling
 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 400px;
  height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  overflow: hidden;
}

/* Side panel mode adjustments */
body.side-panel-mode {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  min-width: 100vw !important;
  overflow: hidden;
}

body.side-panel-mode .popup-container {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  min-width: 100vw !important;
}

/* Ensure all content fills the side panel width */
body.side-panel-mode .popup-header,
body.side-panel-mode .popup-content,
body.side-panel-mode .screen,
body.side-panel-mode .chat-messages,
body.side-panel-mode .input-area {
  width: 100% !important;
  max-width: none !important;
}

/* Adjust chat messages for side panel */
body.side-panel-mode .chat-messages {
  max-height: calc(100vh - 180px) !important;
}

/* Ensure input area spans full width */
body.side-panel-mode .input-container {
  width: 100% !important;
}

/* Remove any fixed widths that might cause white space */
body.side-panel-mode * {
  max-width: none !important;
}

/* Ensure background covers full width */
body.side-panel-mode {
  background: #f8f9fa !important;
}

body.side-panel-mode .popup-container {
  background: #f8f9fa !important;
}

/* Force full width for all major containers */
body.side-panel-mode .popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Ensure no horizontal scrolling */
body.side-panel-mode {
  overflow-x: hidden !important;
}

body.side-panel-mode .popup-container {
  overflow-x: hidden !important;
}

.popup-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffd700;
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background: #00ff88;
  animation: none;
}

.status-dot.error {
  background: #ff4757;
  animation: none;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Main Content */
.popup-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.screen {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Configuration Screen */
.config-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.config-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.config-message h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 8px;
}

.config-message p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.primary-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.primary-btn:hover {
  background: #5a6fd8;
}

/* Selected Text Container */
.selected-text-container {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  margin: 12px;
  overflow: hidden;
}

.selected-text-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2196f3;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.clear-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.selected-text-content {
  padding: 12px;
  font-size: 13px;
  line-height: 1.4;
  color: #1565c0;
  max-height: 80px;
  overflow-y: auto;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 12px;
  scroll-behavior: smooth;
  max-height: calc(100vh - 200px); /* Ensure it has a maximum height */
}

.welcome-message {
  text-align: center;
  padding: 32px 16px;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-message h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 8px;
}

.welcome-message p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-action-btn {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  text-align: left;
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  background: #f5f5f5;
  border-color: #667eea;
}

.message {
  margin-bottom: 16px;
}

.message-user {
  background: #667eea;
  color: white;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  margin-left: 40px;
  font-size: 14px;
  line-height: 1.4;
}

.message-ai {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  border-radius: 18px 18px 18px 4px;
  margin-right: 40px;
  font-size: 14px;
  line-height: 1.5;
  position: relative;
}

.message-ai .provider-badge {
  position: absolute;
  top: -8px;
  right: 12px;
  background: #667eea;
  color: white;
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* Input Area */
.input-area {
  border-top: 1px solid #e0e0e0;
  background: white;
  padding: 12px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

#questionInput {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 12px 16px;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  max-height: 100px;
  min-height: 40px;
  outline: none;
  transition: border-color 0.2s ease;
}

#questionInput:focus {
  border-color: #667eea;
}

.send-btn {
  background: #667eea;
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: #666;
}

.provider-info {
  font-weight: 500;
}

/* History Screen */
.screen-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.back-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.back-btn:hover {
  background: #f5f5f5;
}

.screen-header h2 {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.clear-history-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-history-btn:hover {
  background: #ff3838;
}

.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.history-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-item-date {
  font-size: 11px;
  color: #666;
}

.history-item-provider {
  font-size: 10px;
  background: #667eea;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
}

.history-item-question {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-item-context {
  font-size: 11px;
  color: #666;
  font-style: italic;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #666;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar,
.history-content::-webkit-scrollbar,
.selected-text-content::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track,
.history-content::-webkit-scrollbar-track,
.selected-text-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
  margin: 4px 0;
}

.chat-messages::-webkit-scrollbar-thumb,
.history-content::-webkit-scrollbar-thumb,
.selected-text-content::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 4px;
  border: 1px solid #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.history-content::-webkit-scrollbar-thumb:hover,
.selected-text-content::-webkit-scrollbar-thumb:hover {
  background: #5a6fd8;
}

/* Scroll indicator for better UX */
.chat-messages {
  position: relative;
}

.chat-messages::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.1), transparent);
  pointer-events: none;
}

/* Scroll to bottom button */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-to-bottom-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.scroll-to-bottom-btn:active {
  transform: scale(0.95);
}

/* AI Response Formatting */
.thinking-section {
  margin: 12px 0;
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  background: #f8f9fa;
}

.thinking-toggle {
  width: 100%;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: none;
  padding: 10px 12px;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  font-weight: 500;
  color: #1565c0;
  transition: all 0.2s ease;
}

.thinking-toggle:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
}

.thinking-toggle.thinking-expanded {
  border-radius: 8px 8px 0 0;
}

.thinking-arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.thinking-content {
  border-top: 1px solid #e3f2fd;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.thinking-text {
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  color: #666;
  font-style: italic;
  border-left: 3px solid #2196f3;
  margin-left: 8px;
  background: white;
  border-radius: 4px;
}

/* Code Block Styling */
.code-block-container {
  margin: 12px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2c3e50;
  color: white;
  font-size: 12px;
}

.code-language {
  font-weight: 500;
  text-transform: uppercase;
  color: #3498db;
}

.copy-code-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.2s ease;
}

.copy-code-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.code-block {
  margin: 0;
  padding: 12px;
  background: #2c3e50;
  color: #ecf0f1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
}

.inline-code {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e74c3c;
  border: 1px solid #e0e0e0;
}

/* Response Headers */
.response-header {
  color: #2c3e50;
  margin: 16px 0 8px 0;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 4px;
}

/* Response Lists */
.response-list {
  margin: 8px 0;
  padding-left: 20px;
}

.response-list-item {
  margin: 4px 0;
  color: #333;
}
