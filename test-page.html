<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal AI Mentor - Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        
        .highlight-text {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
            cursor: pointer;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .test-item {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Universal AI Mentor Test Page</h1>
        
        <div class="instructions">
            <h3>How to Test the Extension</h3>
            <ol>
                <li>Make sure the Universal AI Mentor extension is installed and configured</li>
                <li>Try the different test scenarios below</li>
                <li>Check that the extension responds appropriately</li>
                <li>Report any issues you encounter</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>📝 Text Selection Tests</h2>
            <p>Highlight the text below and right-click to test the context menu functionality:</p>
            
            <div class="test-item">
                <strong>Simple Text:</strong>
                <div class="highlight-text">
                    This is a simple sentence that you can highlight to test the AI mentor extension.
                </div>
            </div>
            
            <div class="test-item">
                <strong>Technical Concept:</strong>
                <div class="highlight-text">
                    Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.
                </div>
            </div>
            
            <div class="test-item">
                <strong>Complex Paragraph:</strong>
                <div class="highlight-text">
                    The transformer architecture, introduced in the paper "Attention Is All You Need," revolutionized natural language processing by replacing recurrent neural networks with self-attention mechanisms. This innovation enabled parallel processing of sequences and led to the development of large language models like GPT and BERT.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>💻 Code Examples</h2>
            <p>Try highlighting and asking about these code snippets:</p>
            
            <div class="test-item">
                <strong>JavaScript Function:</strong>
                <div class="code-block">function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}</div>
            </div>
            
            <div class="test-item">
                <strong>Python Class:</strong>
                <div class="code-block">class BinaryTree:
    def __init__(self, value):
        self.value = value
        self.left = None
        self.right = None
    
    def insert(self, value):
        if value < self.value:
            if self.left is None:
                self.left = BinaryTree(value)
            else:
                self.left.insert(value)</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧮 Mathematical Concepts</h2>
            <p>Test the AI's ability to explain mathematical concepts:</p>
            
            <div class="test-item">
                <strong>Calculus:</strong>
                <div class="highlight-text">
                    The derivative of a function f(x) at point x is defined as the limit of [f(x+h) - f(x)]/h as h approaches zero.
                </div>
            </div>
            
            <div class="test-item">
                <strong>Statistics:</strong>
                <div class="highlight-text">
                    The central limit theorem states that the sampling distribution of the sample mean approaches a normal distribution as the sample size increases, regardless of the population distribution.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔬 Scientific Concepts</h2>
            <p>Test explanations of scientific principles:</p>
            
            <div class="test-item">
                <strong>Physics:</strong>
                <div class="highlight-text">
                    Quantum entanglement is a phenomenon where two or more particles become correlated in such a way that the quantum state of each particle cannot be described independently.
                </div>
            </div>
            
            <div class="test-item">
                <strong>Biology:</strong>
                <div class="highlight-text">
                    CRISPR-Cas9 is a revolutionary gene-editing technology that allows scientists to make precise changes to DNA by cutting specific sequences and replacing them with new genetic material.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Extension Popup Tests</h2>
            <p>Click the extension icon and try these test scenarios:</p>
            
            <div class="test-item">
                <span class="status pending">Test 1</span>
                <strong>Basic Question:</strong> Ask "What is artificial intelligence?"
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 2</span>
                <strong>Follow-up:</strong> Ask a follow-up question to test conversation flow
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 3</span>
                <strong>Selected Text:</strong> Highlight text on this page, then open popup to see if it's detected
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 4</span>
                <strong>Quick Actions:</strong> Try the pre-built prompt buttons
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 5</span>
                <strong>History:</strong> Check if conversations are saved in history
            </div>
        </div>

        <div class="test-section">
            <h2>⌨️ Keyboard Shortcut Tests</h2>
            <p>Test keyboard shortcuts:</p>
            
            <div class="test-item">
                <span class="status pending">Test 1</span>
                <strong>Ctrl+Shift+A:</strong> Highlight text and press Ctrl+Shift+A (Cmd+Shift+A on Mac)
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 2</span>
                <strong>Ctrl+Enter:</strong> In popup, type a message and press Ctrl+Enter to send
            </div>
        </div>

        <div class="test-section">
            <h2>⚙️ Settings Tests</h2>
            <p>Test the settings functionality:</p>
            
            <div class="test-item">
                <span class="status pending">Test 1</span>
                <strong>API Configuration:</strong> Open settings and verify your API is configured
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 2</span>
                <strong>Model Selection:</strong> Try changing the AI model
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 3</span>
                <strong>API Test:</strong> Use the "Test API Connection" button
            </div>
            
            <div class="test-item">
                <span class="status pending">Test 4</span>
                <strong>Preferences:</strong> Toggle context menu and auto-select options
            </div>
        </div>

        <div class="instructions">
            <h3>Expected Behavior</h3>
            <ul>
                <li><strong>Text Selection:</strong> Right-click menu should show "Ask AI Mentor about [text]"</li>
                <li><strong>Response Display:</strong> AI responses should appear in a clean overlay</li>
                <li><strong>Popup Interface:</strong> Should open smoothly with current page context</li>
                <li><strong>Settings:</strong> Should save and persist across browser sessions</li>
                <li><strong>Error Handling:</strong> Should show helpful error messages if something goes wrong</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>Troubleshooting</h3>
            <p>If something doesn't work:</p>
            <ol>
                <li>Check that the extension is enabled in your browser</li>
                <li>Verify your API key is configured correctly</li>
                <li>Refresh this page and try again</li>
                <li>Check the browser console for error messages</li>
                <li>Try a different website to test</li>
            </ol>
        </div>
    </div>

    <script>
        // Add some interactivity to make testing more engaging
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers to highlight test sections
            const highlightTexts = document.querySelectorAll('.highlight-text');
            highlightTexts.forEach(text => {
                text.addEventListener('click', function() {
                    // Select the text when clicked
                    const range = document.createRange();
                    range.selectNodeContents(this);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    // Add visual feedback
                    this.style.background = '#667eea';
                    this.style.color = 'white';
                    setTimeout(() => {
                        this.style.background = '#fff3cd';
                        this.style.color = 'inherit';
                    }, 1000);
                });
            });
            
            // Add status update functionality
            window.updateTestStatus = function(testNumber, status) {
                const statusElements = document.querySelectorAll('.status');
                if (statusElements[testNumber - 1]) {
                    statusElements[testNumber - 1].className = `status ${status}`;
                    statusElements[testNumber - 1].textContent = status === 'success' ? '✓ Passed' : 
                                                                status === 'error' ? '✗ Failed' : 'Pending';
                }
            };
        });
    </script>
</body>
</html>
