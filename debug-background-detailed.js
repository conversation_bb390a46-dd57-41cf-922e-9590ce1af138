/**
 * Detailed Background Debug
 * Run this in the popup console to get detailed background script info
 */

async function debugBackgroundDetailed() {
  console.log('🔍 Detailed Background Script Debug...');
  
  try {
    // Test 1: Basic communication
    console.log('1. Testing basic communication...');
    
    try {
      const response = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      console.log('Basic response:', response);
      
      if (response && typeof response === 'object') {
        console.log('✅ Background script is responding');
      } else {
        console.error('❌ Invalid response from background');
        return;
      }
    } catch (error) {
      console.error('❌ Background not responding:', error);
      return;
    }
    
    // Test 2: Check what happens with GET_API_CONFIG
    console.log('\n2. Testing GET_API_CONFIG specifically...');
    
    try {
      const configResponse = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
      console.log('Config response:', configResponse);
      
      if (configResponse && configResponse.success) {
        console.log('✅ GET_API_CONFIG working');
      } else {
        console.error('❌ GET_API_CONFIG failed:', configResponse);
      }
    } catch (error) {
      console.error('❌ GET_API_CONFIG error:', error);
      
      if (error.message && error.message.includes('getAPIConfig')) {
        console.error('🚨 CONFIRMED: StorageManager.getAPIConfig is undefined');
        console.error('This means StorageManager class is not properly loaded');
      }
    }
    
    // Test 3: Try a simple message that doesn't use StorageManager
    console.log('\n3. Testing simple message...');
    
    try {
      const simpleResponse = await chrome.runtime.sendMessage({ type: 'UNKNOWN_TYPE' });
      console.log('Simple response:', simpleResponse);
      
      if (simpleResponse && simpleResponse.error === 'Unknown message type') {
        console.log('✅ Message handling is working, issue is with StorageManager');
      }
    } catch (error) {
      console.error('❌ Even simple messages failing:', error);
    }
    
    // Test 4: Check storage directly
    console.log('\n4. Checking storage directly...');
    
    try {
      const storageResult = await chrome.storage.sync.get(['apiConfig']);
      console.log('Direct storage access:', storageResult);
      
      if (storageResult.apiConfig) {
        console.log('✅ Storage is accessible and has config');
        console.log('Config preview:', {
          provider: storageResult.apiConfig.provider,
          hasApiKey: !!storageResult.apiConfig.apiKey,
          model: storageResult.apiConfig.model
        });
      } else {
        console.error('❌ No config in storage');
      }
    } catch (error) {
      console.error('❌ Storage access failed:', error);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
  
  // Instructions for checking background console
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Go to chrome://extensions/');
  console.log('2. Find "Universal AI Mentor"');
  console.log('3. Click "Inspect views: service worker"');
  console.log('4. Check the console for these messages:');
  console.log('   - "✅ Scripts imported successfully"');
  console.log('   - "✅ LLMAPIClient class loaded"');
  console.log('   - "✅ StorageManager class loaded"');
  console.log('   - "✅ LLM Client initialized"');
  console.log('   - "✅ Storage Manager initialized"');
  console.log('');
  console.log('If you see "❌ StorageManager class not found", that\'s the problem!');
}

// Function to manually test StorageManager
async function testStorageManagerDirectly() {
  console.log('\n🧪 Testing StorageManager directly...');
  
  try {
    // Try to create StorageManager in popup context
    if (typeof StorageManager !== 'undefined') {
      console.log('✅ StorageManager available in popup');
      
      const storageManager = new StorageManager();
      const config = await storageManager.getAPIConfig();
      
      console.log('Direct StorageManager test:', config);
      
      if (config) {
        console.log('✅ StorageManager working in popup context');
      } else {
        console.log('⚠️ StorageManager working but no config found');
      }
    } else {
      console.error('❌ StorageManager not available in popup');
    }
  } catch (error) {
    console.error('❌ StorageManager test failed:', error);
  }
}

// Function to check if scripts are loaded in popup
function checkPopupScripts() {
  console.log('\n📋 Checking popup script availability...');
  
  console.log('LLMAPIClient in popup:', typeof LLMAPIClient !== 'undefined');
  console.log('StorageManager in popup:', typeof StorageManager !== 'undefined');
  
  if (typeof LLMAPIClient !== 'undefined') {
    console.log('✅ LLMAPIClient available in popup');
  }
  
  if (typeof StorageManager !== 'undefined') {
    console.log('✅ StorageManager available in popup');
    testStorageManagerDirectly();
  }
}

// Auto-run debug
debugBackgroundDetailed();
checkPopupScripts();

// Export for manual use
window.debugBackgroundDetailed = debugBackgroundDetailed;
window.testStorageManagerDirectly = testStorageManagerDirectly;
window.checkPopupScripts = checkPopupScripts;
