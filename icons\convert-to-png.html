<!DOCTYPE html>
<html>
<head>
    <title>Convert SVG to PNG Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 10px; display: inline-block; text-align: center; }
        button { margin: 5px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #5a6fd8; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Icon Converter</h1>
    <p>Click the button below to generate all required PNG icons:</p>
    
    <button onclick="generateAllIcons()">Generate All PNG Icons</button>
    
    <div id="status" class="status">Ready to generate icons...</div>
    
    <div id="previews"></div>

    <script>
        function createSVG(size) {
            return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad${size}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="${size}" height="${size}" rx="${Math.round(size * 0.2)}" fill="url(#grad${size})"/>
                <circle cx="${size/2}" cy="${size * 0.35}" r="${size * 0.1}" fill="white" opacity="0.9"/>
                <path d="M${size * 0.3} ${size * 0.6}c0-${size * 0.1} ${size * 0.08}-${size * 0.18} ${size * 0.2}-${size * 0.18}s${size * 0.2} ${size * 0.08} ${size * 0.2} ${size * 0.18}v${size * 0.03}c0 ${size * 0.04}-${size * 0.02} ${size * 0.06}-${size * 0.06} ${size * 0.06}H${size * 0.36}c-${size * 0.04} 0-${size * 0.06}-${size * 0.02}-${size * 0.06}-${size * 0.06}V${size * 0.6}z" fill="white" opacity="0.9"/>
                <circle cx="${size * 0.4}" cy="${size * 0.47}" r="${size * 0.025}" fill="#667eea"/>
                <circle cx="${size * 0.6}" cy="${size * 0.47}" r="${size * 0.025}" fill="#667eea"/>
                <path d="M${size * 0.42} ${size * 0.53}c${size * 0.02} ${size * 0.02} ${size * 0.04} ${size * 0.03} ${size * 0.08} ${size * 0.03}s${size * 0.06}-${size * 0.01} ${size * 0.08}-${size * 0.03}" stroke="white" stroke-width="${size * 0.03}" fill="none" stroke-linecap="round"/>
            </svg>`;
        }

        function svgToPng(svgString, size) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                canvas.width = size;
                canvas.height = size;
                
                img.onload = function() {
                    ctx.drawImage(img, 0, 0, size, size);
                    canvas.toBlob(resolve, 'image/png');
                };
                
                const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
                const url = URL.createObjectURL(svgBlob);
                img.src = url;
            });
        }

        async function generateAllIcons() {
            const sizes = [16, 32, 48, 128];
            const status = document.getElementById('status');
            const previews = document.getElementById('previews');
            
            status.innerHTML = 'Generating icons...';
            previews.innerHTML = '';
            
            for (const size of sizes) {
                try {
                    status.innerHTML = `Generating icon${size}.png...`;
                    
                    const svg = createSVG(size);
                    const blob = await svgToPng(svg, size);
                    
                    // Create download link
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `icon${size}.png`;
                    a.textContent = `Download icon${size}.png`;
                    a.style.display = 'block';
                    a.style.margin = '5px 0';
                    
                    // Create preview
                    const preview = document.createElement('div');
                    preview.className = 'icon-preview';
                    preview.innerHTML = `
                        <div>${size}x${size}</div>
                        <img src="${url}" style="border: 1px solid #ccc; margin: 5px;">
                        <br>
                    `;
                    preview.appendChild(a);
                    
                    previews.appendChild(preview);
                    
                    // Auto-download
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    
                } catch (error) {
                    console.error(`Error generating icon${size}.png:`, error);
                    status.innerHTML += `<br>Error generating icon${size}.png: ${error.message}`;
                }
            }
            
            status.innerHTML = 'All icons generated! Check your downloads folder and move the PNG files to the icons/ directory.';
        }
    </script>
</body>
</html>
