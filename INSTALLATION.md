# Installation Guide

## Quick Start

### 1. Prepare the Extension
1. Ensure all files are in the correct directory structure
2. Create icon files (see `icons/README.md` for instructions)
3. Verify all JavaScript files are present and error-free

### 2. Load in Browser

#### Chrome/Edge/Brave
1. Open your browser and navigate to:
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`
   - Brave: `brave://extensions/`

2. Enable "Developer mode" (toggle in top-right corner)

3. Click "Load unpacked" button

4. Select the extension directory (the folder containing `manifest.json`)

5. The extension should appear in your extensions list

#### Firefox
1. Navigate to `about:debugging`
2. Click "This Firefox"
3. Click "Load Temporary Add-on"
4. Select the `manifest.json` file
5. The extension will be loaded temporarily

### 3. Initial Setup
1. Click the extension icon in your browser toolbar
2. Click "Open Settings" to configure your API
3. Choose your AI provider (OpenAI, Anthropic, Google, or Custom)
4. Enter your API key
5. Select your preferred model
6. Click "Test API Connection" to verify
7. Save your settings

## API Key Setup

### OpenAI
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in or create an account
3. Click "Create new secret key"
4. Copy the key and paste it in the extension settings
5. Choose a model (GPT-4, GPT-3.5-turbo, etc.)

### Anthropic (Claude)
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Sign in or create an account
3. Navigate to API Keys section
4. Create a new API key
5. Copy and paste in extension settings
6. Select Claude model (Opus, Sonnet, or Haiku)

### Google Gemini
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy and paste in extension settings
5. Choose Gemini Pro or Gemini Pro Vision

### GroqCloud
1. Visit [GroqCloud Console](https://console.groq.com/keys)
2. Sign in or create a free account
3. Click "Create API Key"
4. Copy the API key and paste in extension settings
5. Select from ultra-fast models like Llama 3.3 70B or Gemma2 9B
6. Enjoy lightning-fast AI responses!

### Custom API
1. Enter your custom API base URL (e.g., `https://api.example.com/v1`)
2. Provide your API key
3. Specify the model name as expected by your API
4. Test the connection

## Testing the Extension

### Basic Functionality Test
1. **Popup Test**:
   - Click the extension icon
   - Type a simple question like "Hello, how are you?"
   - Press Enter or click Send
   - Verify you get a response

2. **Text Selection Test**:
   - Go to any webpage
   - Highlight some text
   - Right-click and select "Ask AI Mentor about [text]"
   - Verify the response appears in an overlay

3. **Settings Test**:
   - Open extension settings
   - Try changing the model
   - Test API connection
   - Save and verify settings persist

### Advanced Testing
1. **Keyboard Shortcuts**:
   - Select text on a page
   - Press Ctrl+Shift+A (Cmd+Shift+A on Mac)
   - Verify AI response appears

2. **History Test**:
   - Have a few conversations
   - Click the history icon in popup
   - Verify conversations are saved
   - Test clearing history

3. **Error Handling**:
   - Try with invalid API key
   - Test with no internet connection
   - Verify appropriate error messages

## Troubleshooting

### Extension Not Loading
- Check that `manifest.json` is in the root directory
- Verify all file paths in manifest are correct
- Look for JavaScript errors in browser console
- Ensure all required files are present

### API Connection Issues
- Verify API key is correct and has sufficient credits
- Check internet connection
- Ensure API provider service is operational
- Try a different model if available

### Content Script Not Working
- Refresh the webpage after loading extension
- Check if the website blocks content scripts
- Verify permissions in manifest.json
- Look for console errors on the webpage

### Popup Not Opening
- Check for JavaScript errors in popup console
- Verify popup.html file exists and is valid
- Ensure all CSS and JS files are linked correctly

### Settings Not Saving
- Check browser storage permissions
- Verify storage.js is working correctly
- Look for errors in background script console

## Browser Console Debugging

### View Extension Logs
1. **Background Script**:
   - Go to `chrome://extensions/`
   - Find your extension
   - Click "Inspect views: background page"

2. **Popup Script**:
   - Right-click extension icon
   - Select "Inspect popup"

3. **Content Script**:
   - Open webpage developer tools (F12)
   - Check console for content script logs

### Common Error Messages
- `Failed to fetch`: Network or API endpoint issue
- `Invalid API key`: Check your API key configuration
- `Content script not loaded`: Refresh the page
- `Storage error`: Check browser permissions

## Performance Tips

### Optimize for Better Performance
1. **API Usage**:
   - Use appropriate max_tokens settings
   - Choose efficient models for your use case
   - Monitor API usage and costs

2. **Extension Performance**:
   - Keep conversation history reasonable size
   - Clear old conversations periodically
   - Use appropriate temperature settings

3. **Browser Performance**:
   - Close unused extension popups
   - Monitor memory usage in task manager
   - Restart browser if issues persist

## Security Considerations

### API Key Security
- Never share your API keys
- Use environment-specific keys if available
- Monitor API usage for unusual activity
- Rotate keys periodically

### Privacy
- Extension stores data locally only
- No data is sent to third parties
- API calls go directly to chosen provider
- Clear history if sharing computer

## Getting Help

### Before Reporting Issues
1. Check this installation guide
2. Verify all files are present
3. Test with a fresh browser profile
4. Check browser and extension console logs

### Reporting Bugs
Include this information:
- Browser name and version
- Extension version
- Steps to reproduce
- Error messages from console
- API provider being used

### Feature Requests
- Describe the desired functionality
- Explain the use case
- Suggest implementation approach
- Check existing issues first
