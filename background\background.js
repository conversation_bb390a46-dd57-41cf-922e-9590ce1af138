/**
 * Background Service Worker
 * Handles API calls, context menus, and message passing
 */

// Import required modules using importScripts
try {
  importScripts('../lib/llm-api.js', '../lib/storage.js');
  console.log('✅ Scripts imported successfully');

  // Check if classes are available
  console.log('LLMAPIClient available:', typeof LLMAPIClient !== 'undefined');
  console.log('StorageManager available:', typeof StorageManager !== 'undefined');

  if (typeof LLMAPIClient !== 'undefined') {
    console.log('✅ LLMAPIClient class loaded');
  } else {
    console.error('❌ LLMAPIClient class not found');
  }

  if (typeof StorageManager !== 'undefined') {
    console.log('✅ StorageManager class loaded');
  } else {
    console.error('❌ StorageManager class not found');
  }

} catch (error) {
  console.error('❌ Failed to import scripts:', error);
}

class BackgroundService {
  constructor() {
    console.log('🚀 Initializing BackgroundService...');
    this.contextMenuId = 'ai-mentor-context-menu';
    this.isInitialized = false;

    // Initialize dependencies immediately
    this.initializeDependencies();
  }

  initializeDependencies() {
    if (this.isInitialized) return;

    try {
      // Check if classes are available
      if (typeof LLMAPIClient === 'undefined') {
        console.error('❌ LLMAPIClient not available');
        return;
      }

      if (typeof StorageManager === 'undefined') {
        console.error('❌ StorageManager not available');
        return;
      }

      this.llmClient = new LLMAPIClient();
      console.log('✅ LLM Client initialized');

      this.storageManager = new StorageManager();
      console.log('✅ Storage Manager initialized');

      this.isInitialized = true;
      console.log('✅ Dependencies initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize dependencies:', error);
    }
  }

  async init() {
    // Dependencies already initialized in constructor

    // Set up context menu
    await this.setupContextMenu();

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });

    console.log('✅ Background service fully initialized');
    return true; // Indicate successful initialization
  }

  /**
   * Set up context menu for selected text
   */
  async setupContextMenu() {
    try {
      console.log('🔧 Setting up context menu...');

      // Remove existing context menu
      await chrome.contextMenus.removeAll();
      console.log('✅ Removed existing context menus');

      // Get preferences, but use defaults if not available
      let showContextMenu = true; // Default to true

      try {
        if (this.storageManager && this.isInitialized) {
          const preferences = await this.storageManager.getUserPreferences();
          showContextMenu = preferences.showContextMenu;
          console.log('✅ Got preferences, showContextMenu:', showContextMenu);
        } else {
          console.log('⚠️ StorageManager not ready, using default context menu setting');
        }
      } catch (prefError) {
        console.warn('⚠️ Failed to get preferences, using default:', prefError);
      }

      if (showContextMenu) {
        chrome.contextMenus.create({
          id: this.contextMenuId,
          title: 'Ask AI Mentor about "%s"',
          contexts: ['selection'],
          documentUrlPatterns: ['http://*/*', 'https://*/*']
        });
        console.log('✅ Context menu created successfully');
      } else {
        console.log('ℹ️ Context menu disabled in preferences');
      }
    } catch (error) {
      console.error('❌ Failed to setup context menu:', error);
    }
  }

  /**
   * Handle context menu clicks
   */
  setupContextMenuHandler() {
    console.log('🔧 Setting up context menu click handler...');

    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      console.log('🖱️ Context menu clicked:', {
        menuItemId: info.menuItemId,
        selectionText: info.selectionText?.substring(0, 50) + '...',
        pageUrl: info.pageUrl
      });

      if (info.menuItemId === this.contextMenuId && info.selectionText) {
        try {
          console.log('✅ Valid context menu click with selected text');

          // Check if API is configured
          const isConfigured = await this.storageManager.isAPIConfigured();
          if (!isConfigured) {
            console.log('❌ API not configured, showing config needed');
            this.showConfigurationNeeded(tab.id);
            return;
          }

          console.log('✅ API configured, processing request...');

          // Send message to content script to show loading state
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_LOADING',
              text: info.selectionText
            });
          } catch (contentError) {
            console.log('Content script not ready, continuing with API call');
          }

          // Get AI response with timeout
          const config = await this.storageManager.getAPIConfig();

          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 30000)
          );

          const responsePromise = this.llmClient.sendMessage(
            `Please explain or help with this: "${info.selectionText}"`,
            info.selectionText,
            config
          );

          const response = await Promise.race([responsePromise, timeoutPromise]);

          // Send response to content script
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_RESPONSE',
              text: info.selectionText,
              response: response,
              url: info.pageUrl
            });
          } catch (contentError) {
            console.error('Failed to send response to content script:', contentError);
            // Fallback: try to show notification or open popup
            this.showFallbackResponse(response, info.selectionText);
          }

          // Save to history
          await this.storageManager.saveConversation({
            url: info.pageUrl,
            context: info.selectionText,
            question: `Explain: "${info.selectionText}"`,
            response: response,
            provider: config.provider
          });

        } catch (error) {
          console.error('Context menu error:', error);
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_ERROR',
              error: error.message
            });
          } catch (contentError) {
            console.error('Failed to send error to content script:', contentError);
            // Fallback error handling
            this.showFallbackError(error.message);
          }
        }
      }
    });
  }

  /**
   * Handle messages from content scripts and popup
   */
  handleMessage(message, sender, sendResponse) {
    // Handle async operations properly
    (async () => {
      try {
        // Ensure dependencies are loaded
        if (!this.isInitialized) {
          this.initializeDependencies();
        }

        switch (message.type) {
          case 'GET_AI_RESPONSE':
            await this.handleAIRequest(message, sendResponse);
            break;

          case 'GET_API_CONFIG':
            const config = await this.storageManager.getAPIConfig();
            sendResponse({ success: true, config });
            break;

          case 'SAVE_API_CONFIG':
            const saveResult = await this.storageManager.saveAPIConfig(message.config);
            if (saveResult) {
              await this.setupContextMenu(); // Refresh context menu
            }
            sendResponse({ success: saveResult });
            break;

          case 'GET_PREFERENCES':
            const preferences = await this.storageManager.getUserPreferences();
            sendResponse({ success: true, preferences });
            break;

          case 'SAVE_PREFERENCES':
            const prefResult = await this.storageManager.saveUserPreferences(message.preferences);
            if (prefResult) {
              await this.setupContextMenu(); // Refresh context menu
            }
            sendResponse({ success: prefResult });
            break;

          case 'GET_CONVERSATION_HISTORY':
            const history = await this.storageManager.getConversationHistory();
            sendResponse({ success: true, history });
            break;

          case 'CLEAR_HISTORY':
            const clearResult = await this.storageManager.clearConversationHistory();
            sendResponse({ success: clearResult });
            break;

          case 'CHECK_API_STATUS':
            const isConfigured = await this.storageManager.isAPIConfigured();
            sendResponse({ success: true, isConfigured });
            break;

          default:
            sendResponse({ success: false, error: 'Unknown message type' });
        }
      } catch (error) {
        console.error('Message handling error:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    // Return true to indicate we will respond asynchronously
    return true;
  }

  /**
   * Handle AI request from popup or content script
   */
  async handleAIRequest(message, sendResponse) {
    console.log('🤖 Handling AI request:', message);

    try {
      const config = await this.storageManager.getAPIConfig();
      console.log('📋 Config loaded:', {
        provider: config?.provider,
        hasApiKey: !!config?.apiKey,
        model: config?.model
      });

      if (!config) {
        console.error('❌ No API config found');
        sendResponse({
          success: false,
          error: 'API not configured. Please configure your API settings first.'
        });
        return;
      }

      console.log('🚀 Starting LLM request...');

      // Add timeout to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000)
      );

      const responsePromise = this.llmClient.sendMessage(
        message.question,
        message.context || '',
        config
      );

      const response = await Promise.race([responsePromise, timeoutPromise]);
      console.log('✅ LLM response received:', response.substring(0, 100) + '...');

      // Save to conversation history
      await this.storageManager.saveConversation({
        url: message.url || 'Extension Popup',
        context: message.context || '',
        question: message.question,
        response: response,
        provider: config.provider
      });

      sendResponse({
        success: true,
        response: response,
        provider: config.provider
      });

    } catch (error) {
      console.error('❌ AI request error:', error);
      sendResponse({
        success: false,
        error: `${error.message || 'Unknown error occurred'}`
      });
    }
  }

  /**
   * Fallback response display when content script fails
   */
  showFallbackResponse(response, selectedText) {
    // Create a notification or try to open popup with response
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'AI Mentor Response',
        message: `For "${selectedText.substring(0, 50)}...": ${response.substring(0, 100)}...`
      });
    }
  }

  /**
   * Fallback error display when content script fails
   */
  showFallbackError(errorMessage) {
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'AI Mentor Error',
        message: errorMessage
      });
    }
  }

  /**
   * Show configuration needed notification
   */
  showConfigurationNeeded(tabId) {
    chrome.tabs.sendMessage(tabId, {
      type: 'SHOW_CONFIG_NEEDED'
    });
  }

  /**
   * Handle first installation
   */
  async handleFirstInstall() {
    try {
      // Set default preferences
      const defaultPrefs = this.storageManager.getDefaultPreferences();
      await this.storageManager.saveUserPreferences(defaultPrefs);
      
      // Open settings page
      chrome.tabs.create({
        url: chrome.runtime.getURL('settings/settings.html')
      });
    } catch (error) {
      console.error('First install setup error:', error);
    }
  }
}

// Initialize the background service
console.log('🚀 Starting background service initialization...');

const backgroundService = new BackgroundService();

// Set up message handler
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  return backgroundService.handleMessage(message, sender, sendResponse);
});

// Initialize asynchronously
(async function() {
  try {
    await backgroundService.init();
    backgroundService.setupContextMenuHandler();
    console.log('✅ Background service fully ready');
  } catch (error) {
    console.error('❌ Failed to initialize background service:', error);
  }
})();
