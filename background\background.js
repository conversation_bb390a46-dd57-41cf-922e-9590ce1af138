/**
 * Background Service Worker
 * Handles API calls, context menus, and message passing
 */

// Import required modules - load them dynamically
let LLMAPIClient, StorageManager;

async function loadDependencies() {
  try {
    // Load and execute the scripts
    const [llmScript, storageScript] = await Promise.all([
      fetch(chrome.runtime.getURL('lib/llm-api.js')).then(r => r.text()),
      fetch(chrome.runtime.getURL('lib/storage.js')).then(r => r.text())
    ]);

    // Execute the scripts in global scope
    eval(llmScript);
    eval(storageScript);

    console.log('✅ Dependencies loaded successfully');
  } catch (error) {
    console.error('❌ Failed to load dependencies:', error);
  }
}

class BackgroundService {
  constructor() {
    console.log('🚀 Initializing BackgroundService...');
    this.contextMenuId = 'ai-mentor-context-menu';
    this.isInitialized = false;

    this.init();
  }

  async initializeDependencies() {
    if (this.isInitialized) return;

    try {
      await loadDependencies();

      this.llmClient = new LLMAPIClient();
      console.log('✅ LLM Client initialized');

      this.storageManager = new StorageManager();
      console.log('✅ Storage Manager initialized');

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize dependencies:', error);
    }
  }

  async init() {
    // Initialize dependencies first
    await this.initializeDependencies();

    // Set up context menu
    await this.setupContextMenu();

    // Listen for messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      return this.handleMessage(message, sender, sendResponse);
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });

    console.log('✅ Background service fully initialized');
  }

  /**
   * Set up context menu for selected text
   */
  async setupContextMenu() {
    try {
      // Remove existing context menu
      await chrome.contextMenus.removeAll();
      
      const preferences = await this.storageManager.getUserPreferences();
      
      if (preferences.showContextMenu) {
        chrome.contextMenus.create({
          id: this.contextMenuId,
          title: 'Ask AI Mentor about "%s"',
          contexts: ['selection'],
          documentUrlPatterns: ['http://*/*', 'https://*/*']
        });
      }
    } catch (error) {
      console.error('Failed to setup context menu:', error);
    }
  }

  /**
   * Handle context menu clicks
   */
  setupContextMenuHandler() {
    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      if (info.menuItemId === this.contextMenuId && info.selectionText) {
        try {
          // Check if API is configured
          const isConfigured = await this.storageManager.isAPIConfigured();
          if (!isConfigured) {
            this.showConfigurationNeeded(tab.id);
            return;
          }

          // Send message to content script to show loading state
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_LOADING',
              text: info.selectionText
            });
          } catch (contentError) {
            console.log('Content script not ready, continuing with API call');
          }

          // Get AI response with timeout
          const config = await this.storageManager.getAPIConfig();

          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 30000)
          );

          const responsePromise = this.llmClient.sendMessage(
            `Please explain or help with this: "${info.selectionText}"`,
            info.selectionText,
            config
          );

          const response = await Promise.race([responsePromise, timeoutPromise]);

          // Send response to content script
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_RESPONSE',
              text: info.selectionText,
              response: response,
              url: info.pageUrl
            });
          } catch (contentError) {
            console.error('Failed to send response to content script:', contentError);
            // Fallback: try to show notification or open popup
            this.showFallbackResponse(response, info.selectionText);
          }

          // Save to history
          await this.storageManager.saveConversation({
            url: info.pageUrl,
            context: info.selectionText,
            question: `Explain: "${info.selectionText}"`,
            response: response,
            provider: config.provider
          });

        } catch (error) {
          console.error('Context menu error:', error);
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'SHOW_ERROR',
              error: error.message
            });
          } catch (contentError) {
            console.error('Failed to send error to content script:', contentError);
            // Fallback error handling
            this.showFallbackError(error.message);
          }
        }
      }
    });
  }

  /**
   * Handle messages from content scripts and popup
   */
  handleMessage(message, sender, sendResponse) {
    // Handle async operations properly
    (async () => {
      try {
        // Ensure dependencies are loaded
        if (!this.isInitialized) {
          await this.initializeDependencies();
        }

        switch (message.type) {
          case 'GET_AI_RESPONSE':
            await this.handleAIRequest(message, sendResponse);
            break;

          case 'GET_API_CONFIG':
            const config = await this.storageManager.getAPIConfig();
            sendResponse({ success: true, config });
            break;

          case 'SAVE_API_CONFIG':
            const saveResult = await this.storageManager.saveAPIConfig(message.config);
            if (saveResult) {
              await this.setupContextMenu(); // Refresh context menu
            }
            sendResponse({ success: saveResult });
            break;

          case 'GET_PREFERENCES':
            const preferences = await this.storageManager.getUserPreferences();
            sendResponse({ success: true, preferences });
            break;

          case 'SAVE_PREFERENCES':
            const prefResult = await this.storageManager.saveUserPreferences(message.preferences);
            if (prefResult) {
              await this.setupContextMenu(); // Refresh context menu
            }
            sendResponse({ success: prefResult });
            break;

          case 'GET_CONVERSATION_HISTORY':
            const history = await this.storageManager.getConversationHistory();
            sendResponse({ success: true, history });
            break;

          case 'CLEAR_HISTORY':
            const clearResult = await this.storageManager.clearConversationHistory();
            sendResponse({ success: clearResult });
            break;

          case 'CHECK_API_STATUS':
            const isConfigured = await this.storageManager.isAPIConfigured();
            sendResponse({ success: true, isConfigured });
            break;

          default:
            sendResponse({ success: false, error: 'Unknown message type' });
        }
      } catch (error) {
        console.error('Message handling error:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    // Return true to indicate we will respond asynchronously
    return true;
  }

  /**
   * Handle AI request from popup or content script
   */
  async handleAIRequest(message, sendResponse) {
    console.log('🤖 Handling AI request:', message);

    try {
      const config = await this.storageManager.getAPIConfig();
      console.log('📋 Config loaded:', {
        provider: config?.provider,
        hasApiKey: !!config?.apiKey,
        model: config?.model
      });

      if (!config) {
        console.error('❌ No API config found');
        sendResponse({
          success: false,
          error: 'API not configured. Please configure your API settings first.'
        });
        return;
      }

      console.log('🚀 Starting LLM request...');

      // Add timeout to prevent hanging requests
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000)
      );

      const responsePromise = this.llmClient.sendMessage(
        message.question,
        message.context || '',
        config
      );

      const response = await Promise.race([responsePromise, timeoutPromise]);
      console.log('✅ LLM response received:', response.substring(0, 100) + '...');

      // Save to conversation history
      await this.storageManager.saveConversation({
        url: message.url || 'Extension Popup',
        context: message.context || '',
        question: message.question,
        response: response,
        provider: config.provider
      });

      sendResponse({
        success: true,
        response: response,
        provider: config.provider
      });

    } catch (error) {
      console.error('❌ AI request error:', error);
      sendResponse({
        success: false,
        error: `${error.message || 'Unknown error occurred'}`
      });
    }
  }

  /**
   * Fallback response display when content script fails
   */
  showFallbackResponse(response, selectedText) {
    // Create a notification or try to open popup with response
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'AI Mentor Response',
        message: `For "${selectedText.substring(0, 50)}...": ${response.substring(0, 100)}...`
      });
    }
  }

  /**
   * Fallback error display when content script fails
   */
  showFallbackError(errorMessage) {
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'AI Mentor Error',
        message: errorMessage
      });
    }
  }

  /**
   * Show configuration needed notification
   */
  showConfigurationNeeded(tabId) {
    chrome.tabs.sendMessage(tabId, {
      type: 'SHOW_CONFIG_NEEDED'
    });
  }

  /**
   * Handle first installation
   */
  async handleFirstInstall() {
    try {
      // Set default preferences
      const defaultPrefs = this.storageManager.getDefaultPreferences();
      await this.storageManager.saveUserPreferences(defaultPrefs);
      
      // Open settings page
      chrome.tabs.create({
        url: chrome.runtime.getURL('settings/settings.html')
      });
    } catch (error) {
      console.error('First install setup error:', error);
    }
  }
}

// Initialize the background service
const backgroundService = new BackgroundService();

// Set up context menu handler
backgroundService.setupContextMenuHandler();
