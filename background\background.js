/**
 * Background Service Worker
 * Handles API calls, context menus, and message passing
 */

// Import required modules
importScripts('../lib/llm-api.js', '../lib/storage.js');

class BackgroundService {
  constructor() {
    this.llmClient = new LLMAPIClient();
    this.storageManager = new StorageManager();
    this.contextMenuId = 'ai-mentor-context-menu';
    
    this.init();
  }

  init() {
    // Set up context menu
    this.setupContextMenu();
    
    // Listen for messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });
  }

  /**
   * Set up context menu for selected text
   */
  async setupContextMenu() {
    try {
      // Remove existing context menu
      await chrome.contextMenus.removeAll();
      
      const preferences = await this.storageManager.getUserPreferences();
      
      if (preferences.showContextMenu) {
        chrome.contextMenus.create({
          id: this.contextMenuId,
          title: 'Ask AI Mentor about "%s"',
          contexts: ['selection'],
          documentUrlPatterns: ['http://*/*', 'https://*/*']
        });
      }
    } catch (error) {
      console.error('Failed to setup context menu:', error);
    }
  }

  /**
   * Handle context menu clicks
   */
  setupContextMenuHandler() {
    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      if (info.menuItemId === this.contextMenuId && info.selectionText) {
        try {
          // Check if API is configured
          const isConfigured = await this.storageManager.isAPIConfigured();
          if (!isConfigured) {
            this.showConfigurationNeeded(tab.id);
            return;
          }

          // Send message to content script to show loading state
          chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_LOADING',
            text: info.selectionText
          });

          // Get AI response
          const config = await this.storageManager.getAPIConfig();
          const response = await this.llmClient.sendMessage(
            `Please explain or help with this: "${info.selectionText}"`,
            info.selectionText,
            config
          );

          // Send response to content script
          chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_RESPONSE',
            text: info.selectionText,
            response: response,
            url: info.pageUrl
          });

          // Save to history
          await this.storageManager.saveConversation({
            url: info.pageUrl,
            context: info.selectionText,
            question: `Explain: "${info.selectionText}"`,
            response: response,
            provider: config.provider
          });

        } catch (error) {
          console.error('Context menu error:', error);
          chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_ERROR',
            error: error.message
          });
        }
      }
    });
  }

  /**
   * Handle messages from content scripts and popup
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'GET_AI_RESPONSE':
          await this.handleAIRequest(message, sendResponse);
          break;
          
        case 'GET_API_CONFIG':
          const config = await this.storageManager.getAPIConfig();
          sendResponse({ success: true, config });
          break;
          
        case 'SAVE_API_CONFIG':
          const saveResult = await this.storageManager.saveAPIConfig(message.config);
          if (saveResult) {
            await this.setupContextMenu(); // Refresh context menu
          }
          sendResponse({ success: saveResult });
          break;
          
        case 'GET_PREFERENCES':
          const preferences = await this.storageManager.getUserPreferences();
          sendResponse({ success: true, preferences });
          break;
          
        case 'SAVE_PREFERENCES':
          const prefResult = await this.storageManager.saveUserPreferences(message.preferences);
          if (prefResult) {
            await this.setupContextMenu(); // Refresh context menu
          }
          sendResponse({ success: prefResult });
          break;
          
        case 'GET_CONVERSATION_HISTORY':
          const history = await this.storageManager.getConversationHistory();
          sendResponse({ success: true, history });
          break;
          
        case 'CLEAR_HISTORY':
          const clearResult = await this.storageManager.clearConversationHistory();
          sendResponse({ success: clearResult });
          break;
          
        case 'CHECK_API_STATUS':
          const isConfigured = await this.storageManager.isAPIConfigured();
          sendResponse({ success: true, isConfigured });
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Message handling error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * Handle AI request from popup or content script
   */
  async handleAIRequest(message, sendResponse) {
    try {
      const config = await this.storageManager.getAPIConfig();
      
      if (!config) {
        sendResponse({ 
          success: false, 
          error: 'API not configured. Please configure your API settings first.' 
        });
        return;
      }

      const response = await this.llmClient.sendMessage(
        message.question,
        message.context || '',
        config
      );

      // Save to conversation history
      await this.storageManager.saveConversation({
        url: message.url || 'Extension Popup',
        context: message.context || '',
        question: message.question,
        response: response,
        provider: config.provider
      });

      sendResponse({
        success: true,
        response: response,
        provider: config.provider
      });

    } catch (error) {
      console.error('AI request error:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Show configuration needed notification
   */
  showConfigurationNeeded(tabId) {
    chrome.tabs.sendMessage(tabId, {
      type: 'SHOW_CONFIG_NEEDED'
    });
  }

  /**
   * Handle first installation
   */
  async handleFirstInstall() {
    try {
      // Set default preferences
      const defaultPrefs = this.storageManager.getDefaultPreferences();
      await this.storageManager.saveUserPreferences(defaultPrefs);
      
      // Open settings page
      chrome.tabs.create({
        url: chrome.runtime.getURL('settings/settings.html')
      });
    } catch (error) {
      console.error('First install setup error:', error);
    }
  }
}

// Initialize the background service
const backgroundService = new BackgroundService();

// Set up context menu handler
backgroundService.setupContextMenuHandler();
