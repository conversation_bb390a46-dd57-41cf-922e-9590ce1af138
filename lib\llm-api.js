/**
 * Universal LLM API Integration Module
 * Supports multiple LLM providers with a unified interface
 */

class LLMAPIClient {
  constructor() {
    this.supportedProviders = {
      'openai': {
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        defaultModel: 'gpt-3.5-turbo'
      },
      'anthropic': {
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
        defaultModel: 'claude-3-sonnet-20240229'
      },
      'google': {
        name: 'Google Gemini',
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        models: ['gemini-pro', 'gemini-pro-vision'],
        defaultModel: 'gemini-pro'
      },
      'groq': {
        name: 'GroqCloud',
        baseUrl: 'https://api.groq.com/openai/v1',
        models: [
          'llama-3.3-70b-versatile',
          'llama-3.1-8b-instant',
          'gemma2-9b-it',
          'deepseek-r1-distill-llama-70b',
          'qwen-qwq-32b',
          'mistral-saba-24b'
        ],
        defaultModel: 'llama-3.3-70b-versatile'
      },
      'custom': {
        name: 'Custom API',
        baseUrl: '',
        models: [],
        defaultModel: ''
      }
    };

    // Maximum output tokens for each model (set to model's maximum)
    this.modelMaxTokens = {
      // OpenAI Models
      'gpt-4o': 16384,
      'gpt-4o-mini': 16384,
      'gpt-4-turbo': 4096,
      'gpt-4': 8192,
      'gpt-3.5-turbo': 4096,

      // Anthropic Models
      'claude-3-5-sonnet-20241022': 8192,
      'claude-3-5-haiku-20241022': 8192,
      'claude-3-opus-20240229': 4096,
      'claude-3-sonnet-20240229': 4096,
      'claude-3-haiku-20240307': 4096,

      // Google Models
      'gemini-1.5-pro': 8192,
      'gemini-1.5-flash': 8192,
      'gemini-pro': 2048,
      'gemini-pro-vision': 2048,

      // Groq Models
      'llama-3.3-70b-versatile': 32768,
      'llama-3.1-70b-versatile': 32768,
      'llama-3.1-8b-instant': 32768,
      'llama3-70b-8192': 8192,
      'llama3-8b-8192': 8192,
      'mixtral-8x7b-32768': 32768,
      'gemma2-9b-it': 8192,
      'deepseek-r1-distill-llama-70b': 32768,
      'qwen-qwq-32b': 32768,
      'mistral-saba-24b': 24576,

      // Default fallback
      'default': 4096
    };
  }

  /**
   * Get maximum tokens for a specific model
   */
  getMaxTokensForModel(model) {
    const maxTokens = this.modelMaxTokens[model] || this.modelMaxTokens['default'];
    console.log(`🎯 Using max tokens for model ${model}: ${maxTokens}`);
    return maxTokens;
  }

  /**
   * Send a message to the configured LLM
   * @param {string} message - The user's message
   * @param {string} context - Optional context (selected text)
   * @param {Object} config - API configuration
   * @param {Array} conversationHistory - Previous messages in the conversation
   * @returns {Promise<string>} - The LLM's response
   */
  async sendMessage(message, context = '', config, conversationHistory = []) {
    if (!config || !config.apiKey || !config.provider) {
      throw new Error('API configuration is required');
    }

    const provider = this.supportedProviders[config.provider];
    if (!provider) {
      throw new Error(`Unsupported provider: ${config.provider}`);
    }

    const messages = this.buildConversationMessages(message, context, conversationHistory);
    
    try {
      switch (config.provider) {
        case 'openai':
          return await this.callOpenAI(messages, config, provider);
        case 'anthropic':
          return await this.callAnthropic(messages, config, provider);
        case 'google':
          return await this.callGoogle(messages, config, provider);
        case 'groq':
          return await this.callGroq(messages, config, provider);
        case 'custom':
          return await this.callCustomAPI(messages, config);
        default:
          throw new Error(`Provider ${config.provider} not implemented`);
      }
    } catch (error) {
      console.error('LLM API Error:', error);
      throw new Error(`Failed to get response from ${provider.name}: ${error.message}`);
    }
  }

  /**
   * Build conversation messages array for API calls
   */
  buildConversationMessages(message, context, conversationHistory) {
    const messages = [];

    // Add system message
    messages.push({
      role: 'system',
      content: 'You are a helpful AI mentor assistant. Provide clear, concise, and educational responses. Remember the conversation context and refer to previous messages when relevant.'
    });

    // Add conversation history
    conversationHistory.forEach(msg => {
      if (msg.type === 'user') {
        messages.push({
          role: 'user',
          content: msg.content
        });
      } else if (msg.type === 'ai') {
        messages.push({
          role: 'assistant',
          content: msg.content
        });
      }
    });

    // Add current message with context if provided
    let currentMessage = message;
    if (context) {
      currentMessage = `Context: "${context}"\n\nQuestion: ${message}\n\nPlease provide a helpful response based on the context provided.`;
    }

    messages.push({
      role: 'user',
      content: currentMessage
    });

    console.log(`💬 Built conversation with ${messages.length} messages (${conversationHistory.length} from history)`);
    return messages;
  }

  /**
   * Build the prompt with context if provided (legacy method for Google API)
   */
  buildPrompt(message, context) {
    if (context) {
      return `Context: "${context}"\n\nQuestion: ${message}\n\nPlease provide a helpful response based on the context provided.`;
    }
    return message;
  }

  /**
   * Call OpenAI API
   */
  async callOpenAI(messages, config, provider) {
    const model = config.model || provider.defaultModel;
    const maxTokens = this.getMaxTokensForModel(model);

    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: config.temperature || 0.7
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'OpenAI API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Call Anthropic API
   */
  async callAnthropic(messages, config, provider) {
    const model = config.model || provider.defaultModel;
    const maxTokens = this.getMaxTokensForModel(model);

    // Anthropic doesn't use system messages in the messages array, extract it
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    const requestBody = {
      model: model,
      max_tokens: maxTokens,
      messages: conversationMessages
    };

    // Add system message if present
    if (systemMessage) {
      requestBody.system = systemMessage.content;
    }

    const response = await fetch(`${provider.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Anthropic API request failed');
    }

    const data = await response.json();
    return data.content[0].text;
  }

  /**
   * Call Google Gemini API
   */
  async callGoogle(messages, config, provider) {
    // Convert messages to Google's format
    const contents = [];

    messages.forEach(msg => {
      if (msg.role === 'system') {
        // Google doesn't have system messages, prepend to first user message
        return;
      }

      const role = msg.role === 'assistant' ? 'model' : 'user';
      contents.push({
        role: role,
        parts: [{ text: msg.content }]
      });
    });

    // If no contents, create a simple prompt
    if (contents.length === 0) {
      const lastMessage = messages[messages.length - 1];
      contents.push({
        parts: [{ text: lastMessage.content }]
      });
    }

    const response = await fetch(`${provider.baseUrl}/models/${config.model || provider.defaultModel}:generateContent?key=${config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: contents
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Google API request failed');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  /**
   * Call GroqCloud API (OpenAI-compatible)
   */
  async callGroq(messages, config, provider) {
    const model = config.model || provider.defaultModel;
    const maxTokens = this.getMaxTokensForModel(model);

    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: config.temperature || 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'GroqCloud API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Call Custom API (OpenAI-compatible format)
   */
  async callCustomAPI(messages, config) {
    const model = config.model || config.customModel;
    const maxTokens = this.getMaxTokensForModel(model);

    const response = await fetch(`${config.customBaseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: config.temperature || 0.7
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Custom API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Get available models for a provider
   */
  getModelsForProvider(provider) {
    return this.supportedProviders[provider]?.models || [];
  }

  /**
   * Get all supported providers
   */
  getSupportedProviders() {
    return Object.keys(this.supportedProviders).map(key => ({
      id: key,
      name: this.supportedProviders[key].name
    }));
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LLMAPIClient;
} else if (typeof window !== 'undefined') {
  window.LLMAPIClient = LLMAPIClient;
} else {
  // Service worker context - make it globally available
  self.LLMAPIClient = LLMAPIClient;
}
