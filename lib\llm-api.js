/**
 * Universal LLM API Integration Module
 * Supports multiple LLM providers with a unified interface
 */

class LLMAPIClient {
  constructor() {
    this.supportedProviders = {
      'openai': {
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        defaultModel: 'gpt-3.5-turbo'
      },
      'anthropic': {
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
        defaultModel: 'claude-3-sonnet-20240229'
      },
      'google': {
        name: 'Google Gemini',
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        models: ['gemini-pro', 'gemini-pro-vision'],
        defaultModel: 'gemini-pro'
      },
      'custom': {
        name: 'Custom API',
        baseUrl: '',
        models: [],
        defaultModel: ''
      }
    };
  }

  /**
   * Send a message to the configured LLM
   * @param {string} message - The user's message
   * @param {string} context - Optional context (selected text)
   * @param {Object} config - API configuration
   * @returns {Promise<string>} - The LLM's response
   */
  async sendMessage(message, context = '', config) {
    if (!config || !config.apiKey || !config.provider) {
      throw new Error('API configuration is required');
    }

    const provider = this.supportedProviders[config.provider];
    if (!provider) {
      throw new Error(`Unsupported provider: ${config.provider}`);
    }

    const prompt = this.buildPrompt(message, context);
    
    try {
      switch (config.provider) {
        case 'openai':
          return await this.callOpenAI(prompt, config, provider);
        case 'anthropic':
          return await this.callAnthropic(prompt, config, provider);
        case 'google':
          return await this.callGoogle(prompt, config, provider);
        case 'custom':
          return await this.callCustomAPI(prompt, config);
        default:
          throw new Error(`Provider ${config.provider} not implemented`);
      }
    } catch (error) {
      console.error('LLM API Error:', error);
      throw new Error(`Failed to get response from ${provider.name}: ${error.message}`);
    }
  }

  /**
   * Build the prompt with context if provided
   */
  buildPrompt(message, context) {
    if (context) {
      return `Context: "${context}"\n\nQuestion: ${message}\n\nPlease provide a helpful response based on the context provided.`;
    }
    return message;
  }

  /**
   * Call OpenAI API
   */
  async callOpenAI(prompt, config, provider) {
    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || provider.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful AI mentor assistant. Provide clear, concise, and educational responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: config.maxTokens || 1000,
        temperature: config.temperature || 0.7
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'OpenAI API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Call Anthropic API
   */
  async callAnthropic(prompt, config, provider) {
    const response = await fetch(`${provider.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model || provider.defaultModel,
        max_tokens: config.maxTokens || 1000,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Anthropic API request failed');
    }

    const data = await response.json();
    return data.content[0].text;
  }

  /**
   * Call Google Gemini API
   */
  async callGoogle(prompt, config, provider) {
    const response = await fetch(`${provider.baseUrl}/models/${config.model || provider.defaultModel}:generateContent?key=${config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ]
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Google API request failed');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  /**
   * Call Custom API (OpenAI-compatible format)
   */
  async callCustomAPI(prompt, config) {
    const response = await fetch(`${config.customBaseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model || config.customModel,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: config.maxTokens || 1000,
        temperature: config.temperature || 0.7
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Custom API request failed');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Get available models for a provider
   */
  getModelsForProvider(provider) {
    return this.supportedProviders[provider]?.models || [];
  }

  /**
   * Get all supported providers
   */
  getSupportedProviders() {
    return Object.keys(this.supportedProviders).map(key => ({
      id: key,
      name: this.supportedProviders[key].name
    }));
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LLMAPIClient;
} else {
  window.LLMAPIClient = LLMAPIClient;
}
