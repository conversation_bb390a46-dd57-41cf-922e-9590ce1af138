/**
 * Content Script
 * Handles text selection, response display, and page interaction
 */

class ContentScriptManager {
  constructor() {
    this.selectedText = '';
    this.responseContainer = null;
    this.isShowingResponse = false;
    
    this.init();
  }

  init() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });

    // Track text selection
    document.addEventListener('mouseup', () => {
      this.handleTextSelection();
    });

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Close response on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isShowingResponse) {
        this.hideResponse();
      }
    });

    // Close response when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isShowingResponse && this.responseContainer && 
          !this.responseContainer.contains(e.target)) {
        this.hideResponse();
      }
    });
  }

  /**
   * Handle messages from background script
   */
  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'SHOW_LOADING':
        this.showLoadingState(message.text);
        break;
        
      case 'SHOW_RESPONSE':
        this.showResponse(message.text, message.response);
        break;
        
      case 'SHOW_ERROR':
        this.showError(message.error);
        break;
        
      case 'SHOW_CONFIG_NEEDED':
        this.showConfigurationNeeded();
        break;
        
      case 'GET_SELECTED_TEXT':
        sendResponse({ selectedText: this.getSelectedText() });
        break;
        
      case 'GET_PAGE_CONTEXT':
        sendResponse({
          url: window.location.href,
          title: document.title,
          selectedText: this.getSelectedText()
        });
        break;

      case 'PING':
        sendResponse({ status: 'Content script loaded' });
        break;
    }
  }

  /**
   * Handle text selection
   */
  handleTextSelection() {
    const selection = window.getSelection();
    this.selectedText = selection.toString().trim();
    
    // Hide any existing response when new text is selected
    if (this.selectedText && this.isShowingResponse) {
      this.hideResponse();
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeyboardShortcuts(e) {
    // Ctrl+Shift+A (or Cmd+Shift+A on Mac) to ask about selected text
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
      e.preventDefault();
      const selectedText = this.getSelectedText();
      if (selectedText) {
        this.askAboutSelectedText(selectedText);
      }
    }
  }

  /**
   * Get currently selected text
   */
  getSelectedText() {
    return window.getSelection().toString().trim();
  }

  /**
   * Ask AI about selected text
   */
  async askAboutSelectedText(text) {
    try {
      this.showLoadingState(text);
      
      const response = await chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: `Please explain or help with this: "${text}"`,
        context: text,
        url: window.location.href
      });

      if (response.success) {
        this.showResponse(text, response.response);
      } else {
        this.showError(response.error);
      }
    } catch (error) {
      this.showError('Failed to get AI response: ' + error.message);
    }
  }

  /**
   * Show loading state
   */
  showLoadingState(text) {
    this.createResponseContainer();
    this.responseContainer.innerHTML = `
      <div class="ai-mentor-header">
        <div class="ai-mentor-title">AI Mentor</div>
        <button class="ai-mentor-close" onclick="this.closest('.ai-mentor-response').remove()">×</button>
      </div>
      <div class="ai-mentor-content">
        <div class="ai-mentor-context">
          <strong>Selected text:</strong> "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"
        </div>
        <div class="ai-mentor-loading">
          <div class="ai-mentor-spinner"></div>
          <span>Getting AI response...</span>
        </div>
      </div>
    `;
    this.showResponseContainer();

    // Auto-hide loading after 30 seconds to prevent infinite loading
    setTimeout(() => {
      if (this.responseContainer && this.responseContainer.querySelector('.ai-mentor-loading')) {
        this.showError('Request timeout. Please try again.');
      }
    }, 30000);
  }

  /**
   * Show AI response
   */
  showResponse(text, response) {
    this.createResponseContainer();
    this.responseContainer.innerHTML = `
      <div class="ai-mentor-header">
        <div class="ai-mentor-title">AI Mentor</div>
        <div class="ai-mentor-actions">
          <button class="ai-mentor-copy" title="Copy response">📋</button>
          <button class="ai-mentor-close">×</button>
        </div>
      </div>
      <div class="ai-mentor-content">
        <div class="ai-mentor-context">
          <strong>Selected text:</strong> "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}"
        </div>
        <div class="ai-mentor-response-text">
          ${this.formatResponse(response)}
        </div>
      </div>
    `;
    
    // Add event listeners
    this.responseContainer.querySelector('.ai-mentor-close').onclick = () => this.hideResponse();
    this.responseContainer.querySelector('.ai-mentor-copy').onclick = () => this.copyResponse(response);
    
    this.showResponseContainer();
  }

  /**
   * Show error message
   */
  showError(error) {
    this.createResponseContainer();
    this.responseContainer.innerHTML = `
      <div class="ai-mentor-header">
        <div class="ai-mentor-title">AI Mentor - Error</div>
        <button class="ai-mentor-close">×</button>
      </div>
      <div class="ai-mentor-content">
        <div class="ai-mentor-error">
          <strong>Error:</strong> ${error}
        </div>
        <div class="ai-mentor-error-actions">
          <button class="ai-mentor-settings-btn">Open Settings</button>
        </div>
      </div>
    `;
    
    // Add event listeners
    this.responseContainer.querySelector('.ai-mentor-close').onclick = () => this.hideResponse();
    this.responseContainer.querySelector('.ai-mentor-settings-btn').onclick = () => {
      chrome.runtime.sendMessage({ type: 'OPEN_SETTINGS' });
      this.hideResponse();
    };
    
    this.showResponseContainer();
  }

  /**
   * Show configuration needed message
   */
  showConfigurationNeeded() {
    this.createResponseContainer();
    this.responseContainer.innerHTML = `
      <div class="ai-mentor-header">
        <div class="ai-mentor-title">AI Mentor</div>
        <button class="ai-mentor-close">×</button>
      </div>
      <div class="ai-mentor-content">
        <div class="ai-mentor-config-needed">
          <h3>Configuration Required</h3>
          <p>Please configure your API settings to use AI Mentor.</p>
          <button class="ai-mentor-settings-btn">Open Settings</button>
        </div>
      </div>
    `;
    
    // Add event listeners
    this.responseContainer.querySelector('.ai-mentor-close').onclick = () => this.hideResponse();
    this.responseContainer.querySelector('.ai-mentor-settings-btn').onclick = () => {
      chrome.runtime.openOptionsPage();
      this.hideResponse();
    };
    
    this.showResponseContainer();
  }

  /**
   * Create response container
   */
  createResponseContainer() {
    if (this.responseContainer) {
      this.responseContainer.remove();
    }
    
    this.responseContainer = document.createElement('div');
    this.responseContainer.className = 'ai-mentor-response';
    this.responseContainer.id = 'ai-mentor-response-container';
  }

  /**
   * Show response container
   */
  showResponseContainer() {
    document.body.appendChild(this.responseContainer);
    this.isShowingResponse = true;
    
    // Position the container
    this.positionResponseContainer();
    
    // Animate in
    setTimeout(() => {
      this.responseContainer.classList.add('ai-mentor-visible');
    }, 10);
  }

  /**
   * Position response container
   */
  positionResponseContainer() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      // Position below the selected text
      const top = rect.bottom + window.scrollY + 10;
      const left = Math.max(10, rect.left + window.scrollX);
      
      this.responseContainer.style.top = `${top}px`;
      this.responseContainer.style.left = `${left}px`;
      
      // Ensure it doesn't go off screen
      const containerRect = this.responseContainer.getBoundingClientRect();
      if (containerRect.right > window.innerWidth) {
        this.responseContainer.style.left = `${window.innerWidth - containerRect.width - 10}px`;
      }
    } else {
      // Center on screen if no selection
      this.responseContainer.style.top = '50%';
      this.responseContainer.style.left = '50%';
      this.responseContainer.style.transform = 'translate(-50%, -50%)';
    }
  }

  /**
   * Hide response container
   */
  hideResponse() {
    if (this.responseContainer) {
      this.responseContainer.classList.remove('ai-mentor-visible');
      setTimeout(() => {
        if (this.responseContainer) {
          this.responseContainer.remove();
          this.responseContainer = null;
        }
      }, 300);
    }
    this.isShowingResponse = false;
  }

  /**
   * Format response text
   */
  formatResponse(response) {
    let formattedResponse = response;

    // Format thinking sections
    formattedResponse = this.formatThinkingSections(formattedResponse);

    // Format code blocks
    formattedResponse = this.formatCodeBlocks(formattedResponse);

    // Simple markdown-like formatting
    formattedResponse = formattedResponse
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')
      .replace(/\n/g, '<br>');

    return formattedResponse;
  }

  formatThinkingSections(text) {
    // Match <think> sections and make them collapsible
    const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;

    return text.replace(thinkRegex, (_, thinkContent) => {
      const thinkId = 'think-' + Math.random().toString(36).substring(2, 11);

      return `
        <div class="ai-mentor-thinking-section">
          <button class="ai-mentor-thinking-toggle" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'; this.querySelector('.ai-mentor-thinking-arrow').textContent = this.nextElementSibling.style.display === 'none' ? '▼' : '▲';" title="Show/hide AI reasoning">
            🧠 AI Thinking Process
            <span class="ai-mentor-thinking-arrow">▼</span>
          </button>
          <div class="ai-mentor-thinking-content" style="display: none;">
            <div class="ai-mentor-thinking-text">${thinkContent.trim()}</div>
          </div>
        </div>
      `;
    });
  }

  formatCodeBlocks(text) {
    // Match code blocks with language specification
    const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g;

    return text.replace(codeBlockRegex, (_, language, code) => {
      const lang = language || 'text';
      const codeId = 'code-' + Math.random().toString(36).substring(2, 11);

      return `
        <div class="ai-mentor-code-block-container">
          <div class="ai-mentor-code-block-header">
            <span class="ai-mentor-code-language">${lang}</span>
            <button class="ai-mentor-copy-code-btn" onclick="navigator.clipboard.writeText(document.getElementById('${codeId}').textContent).then(() => { this.textContent = '✅ Copied!'; setTimeout(() => this.textContent = '📋 Copy', 2000); })" title="Copy code">
              📋 Copy
            </button>
          </div>
          <pre class="ai-mentor-code-block" id="${codeId}"><code>${code.trim()}</code></pre>
        </div>
      `;
    });
  }

  /**
   * Copy response to clipboard
   */
  async copyResponse(response) {
    try {
      await navigator.clipboard.writeText(response);
      
      // Show feedback
      const copyBtn = this.responseContainer.querySelector('.ai-mentor-copy');
      const originalText = copyBtn.textContent;
      copyBtn.textContent = '✓';
      setTimeout(() => {
        copyBtn.textContent = originalText;
      }, 1000);
    } catch (error) {
      console.error('Failed to copy response:', error);
    }
  }
}

// Initialize content script manager
const contentScriptManager = new ContentScriptManager();
