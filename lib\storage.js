/**
 * Storage Management Utilities
 * Handles secure storage of API keys and user preferences
 */

class StorageManager {
  constructor() {
    this.storageKeys = {
      API_CONFIG: 'apiConfig',
      USER_PREFERENCES: 'userPreferences',
      CONVERSATION_HISTORY: 'conversationHistory'
    };
  }

  /**
   * Save API configuration
   * @param {Object} config - API configuration object
   */
  async saveAPIConfig(config) {
    try {
      await chrome.storage.sync.set({
        [this.storageKeys.API_CONFIG]: config
      });
      return true;
    } catch (error) {
      console.error('Failed to save API config:', error);
      return false;
    }
  }

  /**
   * Get API configuration
   * @returns {Promise<Object|null>} - API configuration or null
   */
  async getAPIConfig() {
    try {
      const result = await chrome.storage.sync.get([this.storageKeys.API_CONFIG]);
      return result[this.storageKeys.API_CONFIG] || null;
    } catch (error) {
      console.error('Failed to get API config:', error);
      return null;
    }
  }

  /**
   * Save user preferences
   * @param {Object} preferences - User preferences object
   */
  async saveUserPreferences(preferences) {
    try {
      await chrome.storage.sync.set({
        [this.storageKeys.USER_PREFERENCES]: preferences
      });
      return true;
    } catch (error) {
      console.error('Failed to save user preferences:', error);
      return false;
    }
  }

  /**
   * Get user preferences
   * @returns {Promise<Object>} - User preferences with defaults
   */
  async getUserPreferences() {
    try {
      const result = await chrome.storage.sync.get([this.storageKeys.USER_PREFERENCES]);
      const defaults = {
        theme: 'light',
        maxTokens: 1000,
        temperature: 0.7,
        showContextMenu: true,
        autoSelectText: true,
        responseFormat: 'markdown'
      };
      return { ...defaults, ...(result[this.storageKeys.USER_PREFERENCES] || {}) };
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Get default preferences
   */
  getDefaultPreferences() {
    return {
      theme: 'light',
      maxTokens: 1000,
      temperature: 0.7,
      showContextMenu: true,
      autoSelectText: true,
      responseFormat: 'markdown'
    };
  }

  /**
   * Save conversation to history
   * @param {Object} conversation - Conversation object
   */
  async saveConversation(conversation) {
    try {
      const history = await this.getConversationHistory();
      const newConversation = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        url: conversation.url,
        context: conversation.context,
        question: conversation.question,
        response: conversation.response,
        provider: conversation.provider
      };
      
      history.unshift(newConversation);
      
      // Keep only last 50 conversations
      const trimmedHistory = history.slice(0, 50);
      
      await chrome.storage.local.set({
        [this.storageKeys.CONVERSATION_HISTORY]: trimmedHistory
      });
      
      return newConversation.id;
    } catch (error) {
      console.error('Failed to save conversation:', error);
      return null;
    }
  }

  /**
   * Get conversation history
   * @returns {Promise<Array>} - Array of conversation objects
   */
  async getConversationHistory() {
    try {
      const result = await chrome.storage.local.get([this.storageKeys.CONVERSATION_HISTORY]);
      return result[this.storageKeys.CONVERSATION_HISTORY] || [];
    } catch (error) {
      console.error('Failed to get conversation history:', error);
      return [];
    }
  }

  /**
   * Clear conversation history
   */
  async clearConversationHistory() {
    try {
      await chrome.storage.local.remove([this.storageKeys.CONVERSATION_HISTORY]);
      return true;
    } catch (error) {
      console.error('Failed to clear conversation history:', error);
      return false;
    }
  }

  /**
   * Check if API is configured
   * @returns {Promise<boolean>} - True if API is configured
   */
  async isAPIConfigured() {
    const config = await this.getAPIConfig();
    return config && config.apiKey && config.provider;
  }

  /**
   * Validate API configuration
   * @param {Object} config - API configuration to validate
   * @returns {Object} - Validation result
   */
  validateAPIConfig(config) {
    const errors = [];
    
    if (!config.provider) {
      errors.push('Provider is required');
    }
    
    if (!config.apiKey || config.apiKey.trim() === '') {
      errors.push('API key is required');
    }
    
    if (config.provider === 'custom') {
      if (!config.customBaseUrl || config.customBaseUrl.trim() === '') {
        errors.push('Custom base URL is required for custom provider');
      }
      if (!config.customModel || config.customModel.trim() === '') {
        errors.push('Custom model name is required for custom provider');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Export user data
   * @returns {Promise<Object>} - Exported data
   */
  async exportUserData() {
    try {
      const [apiConfig, preferences, history] = await Promise.all([
        this.getAPIConfig(),
        this.getUserPreferences(),
        this.getConversationHistory()
      ]);
      
      return {
        apiConfig: apiConfig ? { ...apiConfig, apiKey: '[REDACTED]' } : null,
        preferences,
        conversationHistory: history,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };
    } catch (error) {
      console.error('Failed to export user data:', error);
      throw error;
    }
  }

  /**
   * Import user data (excluding API keys for security)
   * @param {Object} data - Data to import
   */
  async importUserData(data) {
    try {
      if (data.preferences) {
        await this.saveUserPreferences(data.preferences);
      }
      
      if (data.conversationHistory) {
        await chrome.storage.local.set({
          [this.storageKeys.CONVERSATION_HISTORY]: data.conversationHistory
        });
      }
      
      return true;
    } catch (error) {
      console.error('Failed to import user data:', error);
      return false;
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StorageManager;
} else {
  window.StorageManager = StorageManager;
}
