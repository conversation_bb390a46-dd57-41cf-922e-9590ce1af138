/**
 * Test Final Fix
 * Run this in the popup console to test if the service worker context fix worked
 */

async function testFinalFix() {
  console.log('🔧 Testing Final Service Worker Context Fix...');
  
  try {
    // Test 1: Basic communication
    console.log('1. Testing basic background communication...');
    
    const startTime = Date.now();
    
    try {
      const response = await chrome.runtime.sendMessage({ type: 'CHECK_API_STATUS' });
      const responseTime = Date.now() - startTime;
      
      console.log(`Background responding in ${responseTime}ms:`, response);
      
      if (response && response.success !== undefined) {
        console.log('✅ Background script communication working');
      } else {
        console.error('❌ Invalid background response');
        return false;
      }
    } catch (error) {
      console.error('❌ Background not responding:', error);
      return false;
    }
    
    // Test 2: API config retrieval (the problematic one)
    console.log('\n2. Testing API config retrieval...');
    
    try {
      const configResponse = await chrome.runtime.sendMessage({ type: 'GET_API_CONFIG' });
      console.log('Config response:', configResponse);
      
      if (configResponse && configResponse.success) {
        console.log('✅ GET_API_CONFIG working - StorageManager fixed!');
        
        if (configResponse.config) {
          console.log('Config details:', {
            provider: configResponse.config.provider,
            hasApiKey: !!configResponse.config.apiKey,
            model: configResponse.config.model
          });
        }
      } else {
        console.error('❌ GET_API_CONFIG still failing:', configResponse);
      }
    } catch (error) {
      console.error('❌ GET_API_CONFIG error:', error);
      
      if (error.message && error.message.includes('getAPIConfig')) {
        console.error('🚨 STILL BROKEN: StorageManager.getAPIConfig undefined');
        console.error('The service worker context fix didn\'t work');
        return false;
      }
    }
    
    // Test 3: Full AI request
    console.log('\n3. Testing full AI request...');
    
    const aiStartTime = Date.now();
    
    try {
      const aiResponse = await chrome.runtime.sendMessage({
        type: 'GET_AI_RESPONSE',
        question: 'Hello! Please respond with "Fix successful!" if this is working.',
        context: '',
        url: 'Test Page'
      });
      
      const aiResponseTime = Date.now() - aiStartTime;
      
      console.log(`AI request completed in ${aiResponseTime}ms:`, aiResponse);
      
      if (aiResponse && aiResponse.success) {
        console.log('🎉 AI REQUEST SUCCESSFUL!');
        console.log('Response:', aiResponse.response);
        console.log('Provider:', aiResponse.provider);
        
        if (aiResponse.provider === 'groq' && aiResponseTime < 2000) {
          console.log('🚀 GroqCloud ultra-fast response confirmed!');
        }
        
        return true;
      } else {
        console.error('❌ AI request failed:', aiResponse?.error || 'Unknown error');
        return false;
      }
      
    } catch (error) {
      const aiResponseTime = Date.now() - aiStartTime;
      console.error(`❌ AI request error after ${aiResponseTime}ms:`, error);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Function to test popup functionality
async function testPopupFunctionality() {
  console.log('\n🧪 Testing Popup Functionality...');
  
  if (window.popupManager && typeof window.popupManager.sendMessage === 'function') {
    const questionInput = document.getElementById('questionInput');
    
    if (questionInput) {
      const originalValue = questionInput.value;
      questionInput.value = 'Test from popup - please respond briefly';
      
      try {
        console.log('Sending message via popup...');
        await window.popupManager.sendMessage();
        console.log('✅ Popup message sent successfully');
      } catch (error) {
        console.error('❌ Popup message failed:', error);
      } finally {
        questionInput.value = originalValue;
      }
    } else {
      console.error('❌ Question input not found');
    }
  } else {
    console.error('❌ PopupManager not available');
  }
}

// Function to check background console
function checkBackgroundConsole() {
  console.log('\n📋 Background Console Check Instructions:');
  console.log('1. Go to chrome://extensions/');
  console.log('2. Find "Universal AI Mentor"');
  console.log('3. Click "Inspect views: service worker"');
  console.log('4. Look for these SUCCESS messages:');
  console.log('   - "✅ Scripts imported successfully"');
  console.log('   - "✅ LLMAPIClient class loaded"');
  console.log('   - "✅ StorageManager class loaded"');
  console.log('   - "✅ LLM Client initialized"');
  console.log('   - "✅ Storage Manager initialized"');
  console.log('   - "✅ Background service fully ready"');
  console.log('');
  console.log('If you see any "❌" messages, those indicate the remaining issues.');
}

// Auto-run the test
testFinalFix().then(success => {
  console.log('\n📊 FINAL FIX TEST RESULT');
  console.log('=========================');
  
  if (success) {
    console.log('🎉 EXTENSION FULLY FIXED!');
    console.log('✅ Background script working');
    console.log('✅ StorageManager accessible');
    console.log('✅ AI requests successful');
    console.log('✅ No more "getAPIConfig" errors');
    console.log('✅ Extension ready for use!');
    
    // Test popup functionality too
    testPopupFunctionality();
  } else {
    console.log('❌ EXTENSION STILL HAS ISSUES');
    console.log('❌ Background script or AI requests failing');
    console.log('❌ Check background console for details');
    
    checkBackgroundConsole();
  }
});

// Export for manual use
window.testFinalFix = testFinalFix;
window.testPopupFunctionality = testPopupFunctionality;
window.checkBackgroundConsole = checkBackgroundConsole;
