/**
 * Test Response Formatting
 * Run this in the popup console to test the improved AI response formatting
 */

function testResponseFormatting() {
  console.log('🎨 Testing Response Formatting...');
  
  try {
    // Test response with thinking sections and code blocks
    const testResponse = `<think>
The user is asking about easy-to-learn frameworks compared to React. I should provide several alternatives that are beginner-friendly. Let me think about Vue.js, Svelte, Alpine.js, and others. I'll structure this with clear sections and code examples.
</think>

Yes! There are several modern frameworks and libraries that are considered easier to learn than React, especially for beginners. Here are some popular options:

## 1. Vue.js
**Why easier?**
- Simpler syntax and concepts
- Gradual learning curve
- Great documentation

**Example:**
\`\`\`html
<div id="app">
  {{ message }}
</div>
\`\`\`

\`\`\`javascript
new Vue({
  el: '#app',
  data: { message: 'Hello Vue!' }
})
\`\`\`

## 2. Svelte
**Why easier?**
- No virtual DOM complexity
- Compiled approach means **less runtime overhead**
- Intuitive syntax with \`$:\` reactive statements

**Example:**
\`\`\`svelte
<script>
  let count = 0;
</script>

<button on:click={() => count++}>
  Count is {count}
</button>
\`\`\`

## 3. Alpine.js
**Why easier?**
- Lightweight (~30KB) and *no build step* required
- Simple reactivity with \`x-data\` and \`x-text\`
- Works with existing HTML

**Example:**
\`\`\`html
<div x-data="{ count: 0 }">
  <button @click="count++">Count: <span x-text="count"></span></button>
</div>
\`\`\`

### Key Takeaways:
- **Vue.js**: Best for gradual adoption and medium-sized apps
- **Svelte**: Perfect for performance-focused projects
- **Alpine.js**: Ideal for adding interactivity to static sites

Choose based on your project needs and learning style!`;

    // Test the formatting
    if (window.popupManager) {
      console.log('✅ PopupManager found, testing formatting...');
      
      // Add the test response
      window.popupManager.addMessageToChat('ai', testResponse, 'Test Provider');
      
      console.log('✅ Test response added to chat');
      
      // Check if thinking sections are present
      setTimeout(() => {
        const thinkingSections = document.querySelectorAll('.thinking-section');
        const codeBlocks = document.querySelectorAll('.code-block-container');
        const copyButtons = document.querySelectorAll('.copy-code-btn');
        const thinkingToggles = document.querySelectorAll('.thinking-toggle');
        
        console.log('\n📊 Formatting Results:');
        console.log('Thinking sections found:', thinkingSections.length);
        console.log('Code blocks found:', codeBlocks.length);
        console.log('Copy buttons found:', copyButtons.length);
        console.log('Thinking toggles found:', thinkingToggles.length);
        
        if (thinkingSections.length > 0) {
          console.log('✅ Thinking sections properly formatted');
        } else {
          console.log('❌ Thinking sections not found');
        }
        
        if (codeBlocks.length > 0) {
          console.log('✅ Code blocks properly formatted');
        } else {
          console.log('❌ Code blocks not found');
        }
        
        if (copyButtons.length > 0) {
          console.log('✅ Copy buttons added to code blocks');
        } else {
          console.log('❌ Copy buttons not found');
        }
        
        // Test thinking toggle functionality
        if (thinkingToggles.length > 0) {
          console.log('🧪 Testing thinking toggle...');
          thinkingToggles[0].click();
          
          setTimeout(() => {
            const thinkingContent = document.querySelector('.thinking-content');
            if (thinkingContent && thinkingContent.style.display !== 'none') {
              console.log('✅ Thinking toggle works - content visible');
            } else {
              console.log('❌ Thinking toggle not working');
            }
          }, 100);
        }
        
        // Test copy button functionality
        if (copyButtons.length > 0) {
          console.log('🧪 Testing copy button...');
          copyButtons[0].click();
          
          setTimeout(() => {
            if (copyButtons[0].textContent.includes('Copied')) {
              console.log('✅ Copy button works - shows feedback');
            } else {
              console.log('❌ Copy button not working properly');
            }
          }, 100);
        }
        
      }, 500);
      
    } else {
      console.error('❌ PopupManager not found');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Response formatting test failed:', error);
    return false;
  }
}

// Function to test individual formatting functions
function testFormattingFunctions() {
  console.log('\n🔧 Testing Individual Formatting Functions...');
  
  if (!window.popupManager) {
    console.error('❌ PopupManager not found');
    return;
  }
  
  // Test thinking section formatting
  const testThinking = '<think>This is a test thinking section</think>Regular content here';
  const formattedThinking = window.popupManager.formatThinkingSections(testThinking);
  console.log('Thinking formatting result:', formattedThinking.includes('thinking-section'));
  
  // Test code block formatting
  const testCode = '```javascript\nconsole.log("Hello World");\n```';
  const formattedCode = window.popupManager.formatCodeBlocks(testCode);
  console.log('Code block formatting result:', formattedCode.includes('code-block-container'));
  
  // Test full response formatting
  const testResponse = 'This is **bold** and *italic* and `inline code`';
  const formattedResponse = window.popupManager.formatAIResponse(testResponse);
  console.log('Full response formatting result:', formattedResponse.includes('<strong>'));
}

// Function to test different response types
function testDifferentResponseTypes() {
  console.log('\n🎭 Testing Different Response Types...');
  
  if (!window.popupManager) {
    console.error('❌ PopupManager not found');
    return;
  }
  
  // Test 1: Response with multiple thinking sections
  const multiThinkResponse = `<think>First thought process</think>
  
Answer part 1

<think>Second thought process</think>

Answer part 2`;

  // Test 2: Response with mixed code languages
  const mixedCodeResponse = `Here are examples in different languages:

\`\`\`python
print("Hello Python")
\`\`\`

\`\`\`javascript
console.log("Hello JavaScript");
\`\`\`

\`\`\`css
.example { color: blue; }
\`\`\``;

  // Test 3: Response with headers and lists
  const structuredResponse = `# Main Title

## Subtitle

Here are the key points:
- Point one
- Point two  
- Point three

### Subsection

Some **bold** and *italic* text with \`inline code\`.`;

  // Add all test responses
  window.popupManager.addMessageToChat('ai', multiThinkResponse, 'Multi-Think Test');
  window.popupManager.addMessageToChat('ai', mixedCodeResponse, 'Code Test');
  window.popupManager.addMessageToChat('ai', structuredResponse, 'Structure Test');
  
  console.log('✅ Added multiple test response types');
}

// Function to clear test messages
function clearTestMessages() {
  console.log('🧹 Clearing test messages...');
  
  if (window.popupManager) {
    window.popupManager.clearCurrentSession();
    console.log('✅ Test messages cleared');
  } else {
    console.error('❌ PopupManager not found');
  }
}

// Auto-run the test
testResponseFormatting().then(success => {
  console.log('\n📊 RESPONSE FORMATTING TEST RESULT');
  console.log('====================================');
  
  if (success) {
    console.log('✅ RESPONSE FORMATTING WORKING!');
    console.log('✅ Thinking sections are collapsible');
    console.log('✅ Code blocks have copy buttons');
    console.log('✅ Proper markdown formatting');
    console.log('✅ Visual distinction between thinking and answer');
    
    console.log('\n🧪 Additional Tests Available:');
    console.log('- testFormattingFunctions() - Test individual functions');
    console.log('- testDifferentResponseTypes() - Test various response formats');
    console.log('- clearTestMessages() - Clear all test messages');
  } else {
    console.log('❌ RESPONSE FORMATTING ISSUES DETECTED');
    console.log('❌ Check console for specific errors');
  }
});

// Export functions for manual use
window.testResponseFormatting = testResponseFormatting;
window.testFormattingFunctions = testFormattingFunctions;
window.testDifferentResponseTypes = testDifferentResponseTypes;
window.clearTestMessages = clearTestMessages;
