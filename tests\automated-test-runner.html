<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test Runner</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .test-results {
            margin-top: 20px;
        }
        
        .test-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            border-left: 4px solid #ddd;
        }
        
        .test-item.pass {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-item.fail {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-item.running {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .test-status {
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
        }
        
        .test-name {
            flex: 1;
        }
        
        .test-error {
            font-size: 12px;
            color: #721c24;
            margin-top: 5px;
            font-style: italic;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .feature-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        
        .feature-card.verified {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .feature-card.failed {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .feature-description {
            font-size: 13px;
            color: #666;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🧪 Universal AI Mentor Extension Test Suite</h1>
            <p>Comprehensive testing of all claimed features and fixes</p>
        </div>
        
        <div class="test-controls">
            <button id="runAllTests" class="btn btn-primary">Run All Tests</button>
            <button id="runQuickTests" class="btn btn-secondary">Quick Tests</button>
            <button id="clearResults" class="btn btn-secondary">Clear Results</button>
        </div>
        
        <div class="test-summary" id="testSummary" style="display: none;">
            <h3>Test Summary</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="summaryStats"></div>
        </div>
        
        <div class="feature-status" id="featureStatus">
            <div class="feature-card" id="feature-sidepanel">
                <div class="feature-title">📋 Side Panel Stability</div>
                <div class="feature-description">Popup remains stable, side panel support</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
            
            <div class="feature-card" id="feature-loading">
                <div class="feature-title">⏳ Loading State Fix</div>
                <div class="feature-description">No infinite loading, proper timeouts</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
            
            <div class="feature-card" id="feature-persistence">
                <div class="feature-title">💾 Message Persistence</div>
                <div class="feature-description">Conversations survive popup close/open</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
            
            <div class="feature-card" id="feature-history">
                <div class="feature-title">📚 Chat History Interface</div>
                <div class="feature-description">Proper history display and interaction</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
            
            <div class="feature-card" id="feature-textselection">
                <div class="feature-title">🎯 Text Selection Feature</div>
                <div class="feature-description">Context menu works reliably</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
            
            <div class="feature-card" id="feature-groqcloud">
                <div class="feature-title">⚡ GroqCloud Integration</div>
                <div class="feature-description">Ultra-fast AI responses</div>
                <div class="feature-result">Status: Not tested</div>
            </div>
        </div>
        
        <div class="test-results" id="testResults"></div>
        
        <div class="console-output" id="consoleOutput" style="display: none;"></div>
    </div>

    <script src="extension-tests.js"></script>
    <script>
        class TestRunner {
            constructor() {
                this.tester = new ExtensionTester();
                this.setupEventListeners();
                this.consoleOutput = document.getElementById('consoleOutput');
                this.originalConsoleLog = console.log;
                this.setupConsoleCapture();
            }

            setupEventListeners() {
                document.getElementById('runAllTests').addEventListener('click', () => {
                    this.runAllTests();
                });
                
                document.getElementById('runQuickTests').addEventListener('click', () => {
                    this.runQuickTests();
                });
                
                document.getElementById('clearResults').addEventListener('click', () => {
                    this.clearResults();
                });
            }

            setupConsoleCapture() {
                console.log = (...args) => {
                    this.originalConsoleLog(...args);
                    this.appendToConsole(args.join(' '));
                };
            }

            appendToConsole(message) {
                this.consoleOutput.style.display = 'block';
                this.consoleOutput.innerHTML += message + '\n';
                this.consoleOutput.scrollTop = this.consoleOutput.scrollHeight;
            }

            async runAllTests() {
                this.clearResults();
                this.showTestSummary();
                
                try {
                    const results = await this.tester.runAllTests();
                    this.displayResults(results);
                    this.updateFeatureStatus(results);
                } catch (error) {
                    this.appendToConsole(`❌ Test execution failed: ${error.message}`);
                }
            }

            async runQuickTests() {
                this.clearResults();
                this.showTestSummary();
                
                // Run subset of critical tests
                await this.tester.testLoadingStateFix();
                await this.tester.testMessagePersistence();
                await this.tester.testGroqCloudIntegration();
                
                const results = this.tester.generateTestReport();
                this.displayResults(results);
                this.updateFeatureStatus(results);
            }

            showTestSummary() {
                document.getElementById('testSummary').style.display = 'block';
                document.getElementById('consoleOutput').style.display = 'block';
                this.consoleOutput.innerHTML = '';
            }

            displayResults(results) {
                const resultsContainer = document.getElementById('testResults');
                resultsContainer.innerHTML = '';
                
                // Update progress bar
                const progressFill = document.getElementById('progressFill');
                progressFill.style.width = `${results.successRate}%`;
                
                // Update summary stats
                const summaryStats = document.getElementById('summaryStats');
                summaryStats.innerHTML = `
                    <strong>Total Tests:</strong> ${results.total} | 
                    <strong>Passed:</strong> ${results.passed} | 
                    <strong>Failed:</strong> ${results.failed} | 
                    <strong>Success Rate:</strong> ${results.successRate.toFixed(1)}%
                `;
                
                // Display individual test results
                results.results.forEach(test => {
                    const testItem = document.createElement('div');
                    testItem.className = `test-item ${test.status.toLowerCase()}`;
                    
                    testItem.innerHTML = `
                        <div class="test-status">${test.status === 'PASS' ? '✅' : '❌'} ${test.status}</div>
                        <div class="test-name">${test.name}</div>
                        ${test.error ? `<div class="test-error">Error: ${test.error}</div>` : ''}
                    `;
                    
                    resultsContainer.appendChild(testItem);
                });
            }

            updateFeatureStatus(results) {
                const featureMap = {
                    'sidepanel': ['Side Panel', 'side panel', 'toggle'],
                    'loading': ['Loading State', 'timeout', 'spinner'],
                    'persistence': ['Message Persistence', 'session', 'save', 'restore'],
                    'history': ['Chat History', 'history', 'conversation'],
                    'textselection': ['Text Selection', 'context menu', 'selection'],
                    'groqcloud': ['GroqCloud', 'groq', 'llama']
                };

                Object.keys(featureMap).forEach(featureKey => {
                    const featureCard = document.getElementById(`feature-${featureKey}`);
                    const keywords = featureMap[featureKey];
                    
                    const relatedTests = results.results.filter(test => 
                        keywords.some(keyword => 
                            test.name.toLowerCase().includes(keyword.toLowerCase())
                        )
                    );
                    
                    const allPassed = relatedTests.length > 0 && relatedTests.every(test => test.status === 'PASS');
                    const anyFailed = relatedTests.some(test => test.status === 'FAIL');
                    
                    if (allPassed) {
                        featureCard.className = 'feature-card verified';
                        featureCard.querySelector('.feature-result').innerHTML = '✅ Status: VERIFIED';
                    } else if (anyFailed) {
                        featureCard.className = 'feature-card failed';
                        featureCard.querySelector('.feature-result').innerHTML = '❌ Status: FAILED';
                    } else {
                        featureCard.className = 'feature-card';
                        featureCard.querySelector('.feature-result').innerHTML = '⚠️ Status: INCOMPLETE';
                    }
                });
            }

            clearResults() {
                document.getElementById('testResults').innerHTML = '';
                document.getElementById('testSummary').style.display = 'none';
                document.getElementById('consoleOutput').style.display = 'none';
                
                // Reset feature cards
                document.querySelectorAll('.feature-card').forEach(card => {
                    card.className = 'feature-card';
                    card.querySelector('.feature-result').innerHTML = 'Status: Not tested';
                });
                
                // Reset tester
                this.tester = new ExtensionTester();
            }
        }

        // Initialize test runner when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new TestRunner();
        });
    </script>
</body>
</html>
