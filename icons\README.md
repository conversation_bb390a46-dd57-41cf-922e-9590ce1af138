# Extension Icons

This directory should contain the following icon files for the browser extension:

## Required Files
- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows taskbar)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## How to Generate Icons

### Option 1: Use the Icon Generator
1. Open `generate-icons.html` in your browser
2. Right-click on each SVG icon and save as PNG
3. Rename the files to match the required names above

### Option 2: Use Online Tools
1. Visit [Favicon.io](https://favicon.io/) or [RealFaviconGenerator](https://realfavicongenerator.net/)
2. Upload a design or create from text
3. Download the generated icons in the required sizes

### Option 3: Create Custom Icons
Use any image editor (Photoshop, GIMP, Figma, etc.) to create icons with these specifications:

#### Design Guidelines
- **Colors**: Use the brand gradient (#667eea to #764ba2)
- **Theme**: AI/brain/chat bubble/mentor concept
- **Style**: Modern, clean, recognizable at small sizes
- **Background**: Can be transparent or solid color
- **Format**: PNG with transparency support

#### Size Requirements
- All icons should be square (1:1 aspect ratio)
- Use exact pixel dimensions (16x16, 32x32, 48x48, 128x128)
- Ensure crisp edges and good contrast
- Test visibility on both light and dark backgrounds

## Temporary Placeholder
Until you create proper icons, the extension will use default browser icons. The extension will still function normally, but having custom icons improves the user experience and branding.

## Icon Ideas
- 🤖 Robot/AI face
- 💬 Chat bubble with AI symbol
- 🧠 Brain with circuit patterns
- 📚 Book with AI symbol
- ✨ Sparkle/magic wand (representing AI assistance)
- 🎯 Target with AI symbol (representing precision help)

Choose a design that represents the "AI Mentor" concept and is easily recognizable in the browser toolbar.
