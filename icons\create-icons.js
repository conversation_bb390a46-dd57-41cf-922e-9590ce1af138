/**
 * Icon Creator Script
 * Creates PNG icons from SVG for the browser extension
 */

// Function to create an SVG icon
function createSVGIcon(size) {
    const svg = `
        <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="24" height="24" rx="4" fill="url(#grad)"/>
            <circle cx="12" cy="8" r="2.5" fill="white" opacity="0.9"/>
            <path d="M7 14c0-2.8 2.2-5 5-5s5 2.2 5 5v1c0 1.1-.9 2-2 2H9c-1.1 0-2-.9-2-2v-1z" fill="white" opacity="0.9"/>
            <circle cx="9" cy="11" r="0.8" fill="#667eea"/>
            <circle cx="15" cy="11" r="0.8" fill="#667eea"/>
            <path d="M10 13.5c0.5 0.5 1.2 0.8 2 0.8s1.5-0.3 2-0.8" stroke="white" stroke-width="1" fill="none" stroke-linecap="round"/>
        </svg>
    `;
    return svg;
}

// Function to convert SVG to PNG
function svgToPng(svgString, size, callback) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    canvas.width = size;
    canvas.height = size;
    
    img.onload = function() {
        ctx.drawImage(img, 0, 0, size, size);
        canvas.toBlob(callback, 'image/png');
    };
    
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    img.src = url;
}

// Create all required icon sizes
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
    const svg = createSVGIcon(size);
    svgToPng(svg, size, (blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `icon${size}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
});

console.log('Icons created! Check your downloads folder for icon16.png, icon32.png, icon48.png, and icon128.png');
