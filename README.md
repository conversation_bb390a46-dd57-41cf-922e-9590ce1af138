# Universal AI Mentor Browser Extension

A powerful browser extension that provides AI assistance from any LLM provider while browsing the web. Get instant help by highlighting text or asking questions through the popup interface.

## Features

### 🤖 Universal LLM Support
- **OpenAI**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- **Anthropic**: Claude 3 (Opus, Sonnet, Haiku)
- **Google**: Gemini Pro, Gemini Pro Vision
- **GroqCloud**: Llama 3.3, Gemma2, DeepSeek (Ultra-Fast Inference)
- **Custom APIs**: Any OpenAI-compatible API endpoint

### 🎯 Smart Text Interaction
- **Text Selection**: Right-click on any selected text to get AI assistance
- **Context Menu**: Quick access to AI help from anywhere on the web
- **Auto-detection**: Automatically detects selected text when opening popup
- **Keyboard Shortcuts**: Ctrl+Shift+A to ask about selected text

### 💬 Intuitive Chat Interface
- **Modern UI**: Clean, professional chat interface with dark/light themes
- **Code Blocks**: Syntax highlighting with one-click copy functionality
- **AI Thinking**: Toggle to view AI reasoning process (when available)
- **Typing Animation**: Modern typing indicators like Telegram/WhatsApp
- **Quick Actions**: Pre-built prompts for common tasks
- **Conversation History**: Keep track of your AI interactions
- **Response Formatting**: Full markdown support with enhanced styling

### ⚙️ Flexible Configuration
- **API Management**: Secure storage of API keys and settings
- **Model Selection**: Choose from available models for each provider
- **Advanced Settings**: Control temperature, max tokens, and other parameters
- **User Preferences**: Customize behavior and appearance

### 🔒 Privacy & Security
- **Local Storage**: API keys stored securely in browser storage
- **No Data Collection**: Your conversations stay private
- **Export/Import**: Backup and restore your settings and history

## Installation

### From Source
1. Clone or download this repository
2. Open Chrome/Edge and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your browser toolbar

### Configuration
1. Click the extension icon and select "Open Settings"
2. Choose your AI provider (OpenAI, Anthropic, Google, or Custom)
3. Enter your API key
4. Select your preferred model
5. Save settings and start using the extension!

## Usage

### Getting AI Help on Selected Text
1. Highlight any text on a webpage
2. Right-click and select "Ask AI Mentor about [selected text]"
3. View the AI response in an overlay

### Using the Popup Interface
1. Click the extension icon in your browser toolbar
2. Type your question in the input field
3. Press Enter or click Send to get an AI response
4. View conversation history by clicking the history icon

### Keyboard Shortcuts
- **Ctrl+Shift+A** (Cmd+Shift+A on Mac): Ask AI about selected text
- **Ctrl+Enter**: Send message in popup chat

## Supported Providers

### OpenAI
- **Models**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- **API Key**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
- **Documentation**: [OpenAI API Docs](https://platform.openai.com/docs)

### Anthropic
- **Models**: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- **API Key**: Get from [Anthropic Console](https://console.anthropic.com/)
- **Documentation**: [Anthropic API Docs](https://docs.anthropic.com/)

### Google Gemini
- **Models**: Gemini Pro, Gemini Pro Vision
- **API Key**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Documentation**: [Gemini API Docs](https://ai.google.dev/docs)

### GroqCloud
- **Models**: Llama 3.3 70B, Llama 3.1 8B, Gemma2 9B, DeepSeek R1, Qwen QwQ 32B
- **API Key**: Get from [GroqCloud Console](https://console.groq.com/keys)
- **Documentation**: [GroqCloud Docs](https://console.groq.com/docs)
- **Special Features**: Ultra-fast inference, OpenAI-compatible API

### Custom APIs
- **Compatibility**: Any OpenAI-compatible API endpoint
- **Examples**: LocalAI, Ollama with OpenAI compatibility, Azure OpenAI
- **Configuration**: Provide base URL and model name

## Settings

### API Configuration
- **Provider**: Choose your AI service provider
- **API Key**: Your authentication key (stored securely)
- **Model**: Select from available models
- **Custom Endpoint**: For custom API providers

### Advanced Settings
- **Max Tokens**: Control response length (100-4000)
- **Temperature**: Adjust creativity vs. focus (0-1)

### User Preferences
- **Context Menu**: Enable/disable right-click menu
- **Auto-select Text**: Automatically use selected text in popup
- **Theme**: Light, dark, or system preference

## Privacy

This extension prioritizes your privacy:
- **Local Storage**: All data stored locally in your browser
- **No Tracking**: No analytics or user tracking
- **Secure Keys**: API keys encrypted in browser storage
- **No Servers**: Direct communication with AI providers

## Development

### Project Structure
```
├── manifest.json          # Extension manifest
├── background/            # Service worker
├── content/              # Content scripts
├── popup/                # Extension popup
├── settings/             # Settings page
├── lib/                  # Shared libraries
├── styles/               # Common styles
└── icons/                # Extension icons
```

### Key Components
- **LLM API Client**: Universal interface for different AI providers
- **Storage Manager**: Secure settings and history management
- **Content Script**: Text selection and response display
- **Background Service**: API calls and message handling

### Building
No build process required - this is a vanilla JavaScript extension.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues, feature requests, or questions:
1. Check existing GitHub issues
2. Create a new issue with detailed information
3. Include browser version and extension version

## Recent Updates

### UI/UX Improvements
- **Enhanced Code Blocks**: Improved styling with gradient headers, file icons, and better copy functionality
- **Fixed Copy Buttons**: Robust clipboard API with visual feedback and fallback support
- **AI Thinking Toggle**: Working expand/collapse functionality with smooth animations
- **Modern Typing Animation**: Replaced loading text with bouncing dots animation
- **Dark Mode Support**: Comprehensive dark theme with proper contrast and styling
- **Visual Polish**: Professional interface comparable to modern messaging apps

## Changelog

### Version 1.1.0 (Latest)
- **Enhanced UI/UX**: Modern chat interface with improved styling
- **Fixed Copy Functionality**: Robust clipboard operations with visual feedback
- **Working AI Thinking Toggle**: Smooth expand/collapse animations
- **Modern Typing Animation**: Telegram/WhatsApp-style typing indicators
- **Dark Mode Support**: Comprehensive dark theme implementation
- **Code Block Improvements**: Better syntax highlighting and copy buttons
- **Performance Optimizations**: Faster loading and smoother animations

### Version 1.0.0
- Initial release
- Support for OpenAI, Anthropic, Google, GroqCloud, and custom APIs
- Text selection and popup chat interfaces
- Conversation history and settings management
- Cross-browser compatibility (Chrome, Edge, Firefox)
- Ultra-fast inference with GroqCloud integration
