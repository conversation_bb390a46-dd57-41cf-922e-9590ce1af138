/**
 * Immediate Extension Test
 * Run this in the popup console to test all features
 */

async function testExtensionNow() {
  console.log('🧪 Testing Universal AI Mentor Extension...');
  
  const results = {
    passed: 0,
    failed: 0,
    issues: []
  };
  
  function pass(test) {
    results.passed++;
    console.log(`✅ PASS: ${test}`);
  }
  
  function fail(test, error) {
    results.failed++;
    results.issues.push(`${test}: ${error}`);
    console.log(`❌ FAIL: ${test} - ${error}`);
  }
  
  // Test 1: Side Panel Implementation
  console.log('\n📋 Testing Side Panel...');
  
  // Check manifest permissions
  const manifest = chrome.runtime.getManifest();
  if (manifest.permissions.includes('sidePanel')) {
    pass('Side panel permission exists');
  } else {
    fail('Side panel permission', 'sidePanel permission missing from manifest');
  }
  
  // Check side panel configuration
  if (manifest.side_panel && manifest.side_panel.default_path) {
    pass('Side panel configuration exists');
  } else {
    fail('Side panel configuration', 'side_panel config missing from manifest');
  }
  
  // Check for side panel button
  const sidePanelBtn = document.getElementById('sidePanelBtn');
  if (sidePanelBtn) {
    pass('Side panel button exists in DOM');
  } else {
    fail('Side panel button', 'sidePanelBtn not found in DOM');
  }
  
  // Test 2: Loading State Management
  console.log('\n⏳ Testing Loading State...');
  
  // Check if popup manager exists
  if (typeof window.popupManager !== 'undefined') {
    pass('PopupManager instance exists');
    
    // Check loading state flag
    if ('isLoading' in window.popupManager) {
      pass('Loading state flag exists');
    } else {
      fail('Loading state flag', 'isLoading property not found');
    }
  } else {
    fail('PopupManager', 'PopupManager not found in window');
  }
  
  // Check for loading overlay
  const loadingOverlay = document.getElementById('loadingOverlay');
  if (loadingOverlay) {
    pass('Loading overlay exists');
  } else {
    fail('Loading overlay', 'loadingOverlay not found');
  }
  
  // Test 3: Message Persistence
  console.log('\n💾 Testing Message Persistence...');
  
  if (window.popupManager) {
    // Check session methods
    if (typeof window.popupManager.saveCurrentSession === 'function') {
      pass('saveCurrentSession method exists');
    } else {
      fail('saveCurrentSession', 'method not found');
    }
    
    if (typeof window.popupManager.restoreCurrentSession === 'function') {
      pass('restoreCurrentSession method exists');
    } else {
      fail('restoreCurrentSession', 'method not found');
    }
    
    if (Array.isArray(window.popupManager.currentSessionMessages)) {
      pass('currentSessionMessages array exists');
    } else {
      fail('currentSessionMessages', 'array not found or not array');
    }
  }
  
  // Check clear chat button
  const clearChatBtn = document.getElementById('clearChatBtn');
  if (clearChatBtn) {
    pass('Clear chat button exists');
  } else {
    fail('Clear chat button', 'clearChatBtn not found');
  }
  
  // Test storage functionality
  try {
    await chrome.storage.local.set({ testKey: 'testValue' });
    const result = await chrome.storage.local.get(['testKey']);
    if (result.testKey === 'testValue') {
      pass('Storage functionality works');
      await chrome.storage.local.remove(['testKey']);
    } else {
      fail('Storage functionality', 'storage read/write failed');
    }
  } catch (error) {
    fail('Storage functionality', error.message);
  }
  
  // Test 4: UI Elements
  console.log('\n🎨 Testing UI Elements...');
  
  const requiredElements = [
    'chatMessages',
    'questionInput', 
    'sendBtn',
    'settingsBtn',
    'historyBtn',
    'statusIndicator'
  ];
  
  requiredElements.forEach(elementId => {
    const element = document.getElementById(elementId);
    if (element) {
      pass(`${elementId} exists`);
    } else {
      fail(`${elementId}`, 'element not found in DOM');
    }
  });
  
  // Test 5: GroqCloud Integration
  console.log('\n⚡ Testing GroqCloud Integration...');
  
  try {
    const llmResponse = await fetch(chrome.runtime.getURL('lib/llm-api.js'));
    const llmScript = await llmResponse.text();
    
    if (llmScript.includes('groq')) {
      pass('GroqCloud provider exists in LLM client');
    } else {
      fail('GroqCloud provider', 'groq not found in LLM client');
    }
    
    if (llmScript.includes('api.groq.com')) {
      pass('GroqCloud API endpoint configured');
    } else {
      fail('GroqCloud API endpoint', 'api.groq.com not found');
    }
    
    if (llmScript.includes('callGroq')) {
      pass('callGroq method implemented');
    } else {
      fail('callGroq method', 'callGroq not found in LLM client');
    }
    
    if (llmScript.includes('llama-3.3-70b-versatile')) {
      pass('GroqCloud models defined');
    } else {
      fail('GroqCloud models', 'llama-3.3-70b-versatile not found');
    }
  } catch (error) {
    fail('GroqCloud integration', `Failed to load LLM script: ${error.message}`);
  }
  
  // Test 6: Error Handling
  console.log('\n🚨 Testing Error Handling...');
  
  // Check notifications permission
  if (manifest.permissions.includes('notifications')) {
    pass('Notifications permission exists');
  } else {
    fail('Notifications permission', 'notifications permission missing');
  }
  
  // Test 7: Settings Integration
  console.log('\n⚙️ Testing Settings Integration...');
  
  try {
    const settingsResponse = await fetch(chrome.runtime.getURL('settings/settings.html'));
    const settingsHTML = await settingsResponse.text();
    
    if (settingsHTML.includes('GroqCloud') || settingsHTML.includes('groq')) {
      pass('GroqCloud option in settings');
    } else {
      fail('GroqCloud in settings', 'GroqCloud option not found in settings HTML');
    }
  } catch (error) {
    fail('Settings integration', `Failed to load settings: ${error.message}`);
  }
  
  // Generate final report
  console.log('\n📊 TEST RESULTS');
  console.log('================');
  console.log(`Total Tests: ${results.passed + results.failed}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Extension is ready!');
    return { status: 'READY', results };
  } else {
    console.log('\n🚨 TESTS FAILED! Issues found:');
    results.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
    return { status: 'NOT_READY', results };
  }
}

// Auto-run the test
testExtensionNow().then(result => {
  if (result.status === 'READY') {
    console.log('✅ Extension verification complete - ALL FEATURES WORKING!');
  } else {
    console.log('❌ Extension verification failed - FIXES NEEDED!');
  }
});

// Export for manual use
window.testExtensionNow = testExtensionNow;
